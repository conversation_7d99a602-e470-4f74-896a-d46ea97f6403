import { app, BrowserWindow, Menu, MenuItem, shell, ipcMain, nativeTheme, IpcMainEvent, IpcMainInvokeEvent, dialog } from 'electron';
import * as path from 'path';
// Replace electron-is-dev with simple environment check
const isDev = process.env.NODE_ENV === 'development' && process.env.ELECTRON_FORCE_STATIC !== 'true';
import { setupCapacitorElectronPlugins } from './setup';
import { getCapacitorConfig, setupReloadWatcher } from './utils';
import * as fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { autoUpdater } from 'electron-updater';
import * as os from 'os';

// Polyfill for browser globals in Electron main process
(global as any).self = global;
(global as any).window = global;

// Database imports - using pouchdb-browser with polyfill
import PouchDBConstructor from 'pouchdb-browser';
import PouchDBFind from 'pouchdb-find';

// Apply PouchDB plugins
PouchDBConstructor.plugin(PouchDBFind);

// --- CouchDB Server Import ---
import { initCouchDBServer, cleanupCouchDBServer } from './couchdb-server';

// USB Detection - Optional with fallback
let usbDetect: any = null;
try {
  usbDetect = require('usb-detection');
  console.log('✅ USB detection loaded successfully');
} catch (error: any) {
  console.warn('⚠️ USB detection not available (cross-platform build):', error.message);
  // Create a fallback object with stub methods
  usbDetect = {
    startMonitoring: () => console.log('USB monitoring disabled (cross-platform build)'),
    stopMonitoring: () => console.log('USB monitoring disabled (cross-platform build)'),
    find: () => Promise.resolve([])
  };
}

// Keep track of open PouchDB instances in the main process
const pouchDbInstances: Map<string, PouchDB.Database> = new Map();

// Simplified sync supervision system
interface SyncSession {
  handler: any;
  isActive: boolean;
  lastActivity: number;
  retryCount: number;
  maxRetries: number;
}

const activeSyncSessions: Map<string, SyncSession> = new Map();
const SYNC_HEALTH_CHECK_INTERVAL = 30000; // 30 seconds
const MAX_SYNC_RETRIES = 5;
const RETRY_DELAY_BASE = 1000; // 1 second base delay

// Start sync health monitoring
let syncHealthInterval: NodeJS.Timeout | null = null;

// Get Config
const capacitorConfig = getCapacitorConfig();
const appName = capacitorConfig.appName || 'Bistro';

// Setup Plugins
setupCapacitorElectronPlugins();

// 📂 Local database root – resolved at runtime (userData folder)
let dbRootPath = '';

// Device ID for P2P sync
let deviceId = '';

// CouchDB readiness flag
let isCouchDBReady = false;

// Keep a global reference of the window object, if you don't, the window will
// be closed automatically when the JavaScript object is garbage collected.
let mainWindow: BrowserWindow | null = null;

// Prevent multiple registrations of DB IPC handlers
let dbIpcHandlersRegistered = false;

// Simple file:// protocol serving instead of electron-serve to avoid path issues
const loadStaticFile = (window: BrowserWindow) => {
  const indexPath = path.join(__dirname, '..', 'app', 'index.html');
  console.log('Loading static file from:', indexPath);
  return window.loadFile(indexPath);
};

function createWindow(): void {
  // Create the browser window
  const windowOptions = {
    title: appName,
    width: capacitorConfig.plugins?.CapacitorCommunityElectron?.electronOptions?.windowWidth || 1200,
    height: capacitorConfig.plugins?.CapacitorCommunityElectron?.electronOptions?.windowHeight || 800,
    webPreferences: {
      // --- Security Best Practices --- 
      contextIsolation: true, // Required for contextBridge, recommended for security
      nodeIntegration: false, // Disable Node.js integration in renderer for security
      // --- Preload Script --- 
      preload: path.join(__dirname, 'preload.js'),
    },
    show: false,
  };

  mainWindow = new BrowserWindow(windowOptions);

  // Load the app
  if (isDev) {
    // Dev mode - load from dev server
    const startUrl = process.env.ELECTRON_START_URL || 'http://localhost:3000';
    const offlineFallbackEnabled = process.env.ELECTRON_OFFLINE_FALLBACK === 'true';
    
    console.log(`Loading development server at ${startUrl}`);
    console.log(`Offline fallback ${offlineFallbackEnabled ? 'enabled' : 'disabled'}`);
    
    // Set a longer timeout to give server time to start (5 seconds)
    let connectionAttempts = 0;
    const maxAttempts = 10;
    const retryInterval = 1000; // 1 second between attempts
    
    const attemptConnection = () => {
      connectionAttempts++;
      console.log(`Attempt ${connectionAttempts}/${maxAttempts} to connect to ${startUrl}...`);
      
      if (!mainWindow) {
        console.error('Main window is null, cannot attempt connection');
        return;
      }
      
      mainWindow.loadURL(startUrl).then(() => {
        console.log(`Successfully connected to development server at ${startUrl}`);
      }).catch((err) => {
        // If loading fails, retry a few times before showing error
        if (connectionAttempts < maxAttempts) {
          console.log(`Connection failed, retrying in ${retryInterval/1000} seconds...`);
          setTimeout(attemptConnection, retryInterval);
          return;
        }
        
        // After max attempts, show the dialog
        // const { dialog } = require('electron'); // Removed local import
        
        if (offlineFallbackEnabled) {
          if (mainWindow) { // Added null check
            dialog.showMessageBox(mainWindow, {
              type: 'warning' as const,
              title: 'Development Server Unreachable',
              message: 'The Next.js development server is not reachable. Falling back to offline mode.',
              detail: 'For best development experience, start the Next.js server with npm run dev.'
            });
            
            // Try to load from local file as fallback
            console.log('Server unreachable, falling back to local file if available');
            try {
              // Check if the production build exists
              const outIndexPath = path.join(app.getAppPath(), 'out', 'index.html');
              if (fs.existsSync(outIndexPath) && mainWindow) {
                console.log(`Loading from local file at ${outIndexPath}`);
                mainWindow.loadFile(outIndexPath).catch(e => {
                  console.error('Failed to load local file:', e);
                });
              } else {
                console.error('No local build found at', outIndexPath);
                dialog.showErrorBox(
                  'Offline Build Not Found',
                  'No local build files were found. Please run the Next.js build command first.'
                );
              }
            } catch (fallbackErr) {
              console.error('Error loading fallback:', fallbackErr);
            }
          }
        } else {
          if (mainWindow) { // Added null check
            dialog.showErrorBox(
              'Development Server Unreachable',
              'Unable to connect to the Next.js development server after multiple attempts.\n\nMake sure your Next.js server is running with "npm run dev" in another terminal.\n\nAlternatives:\n1. Start Next.js server first, then restart this app\n2. Use hybrid mode with "npm run electron:start-hybrid"\n3. Build for production and run in offline mode'
            );
          }
        }
      });
    };
    
    // Start the connection attempt process
    attemptConnection();
    
    // Open DevTools in development
    mainWindow.webContents.openDevTools();
  } else {
    // Production - serve static files using electron-serve (handles .asar properly)
    console.log('Production: serving static files via electron-serve');
    console.log('- Is packaged:', app.isPackaged);
    console.log('- App path:', app.getAppPath());
    
    if (mainWindow) {
      // 🚨 CRITICAL FIX: Add timeout and retry logic for static file loading
      const loadWithTimeout = async (retryCount = 0): Promise<void> => {
        const maxRetries = 3;
        const timeoutMs = 15000; // 15 second timeout
        
        return new Promise((resolve, reject) => {
          const timeoutId = setTimeout(() => {
            console.error(`[Electron] ⏰ Static file loading timeout (attempt ${retryCount + 1})`);
            reject(new Error(`Static file loading timeout after ${timeoutMs}ms`));
          }, timeoutMs);
          
          loadStaticFile(mainWindow!)
            .then(() => {
              clearTimeout(timeoutId);
              console.log('✅ [Electron] Static files loaded successfully');
              resolve();
            })
            .catch((err: Error) => {
              clearTimeout(timeoutId);
              console.error(`[Electron] ❌ Static file loading failed (attempt ${retryCount + 1}):`, err);
              reject(err);
            });
        });
      };
      
      // Retry logic with exponential backoff
      const attemptLoad = async (attempt = 0): Promise<void> => {
        try {
          await loadWithTimeout(attempt);
        } catch (err) {
          const error = err as Error;
          
          if (attempt < 2) { // Max 3 attempts (0, 1, 2)
            const delay = Math.pow(2, attempt) * 1000; // 1s, 2s, 4s
            console.log(`[Electron] 🔄 Retrying static file load in ${delay}ms...`);
            
            await new Promise(resolve => setTimeout(resolve, delay));
            return attemptLoad(attempt + 1);
          }
          
          // All retries failed - show comprehensive error dialog
          console.error('[Electron] ❌ All static file loading attempts failed');
          // const { dialog } = require('electron'); // Removed local import

          if (!mainWindow) {
            console.error('[Electron] ❌ No main window available for error dialog');
            app.quit();
            return;
          }

          const dialogResult = await dialog.showMessageBox(mainWindow, {
            type: 'error' as const,
            title: 'Failed to Load Application',
            message: 'Could not load the application files after multiple attempts.',
            detail: `Error: ${error.message}\n\nThis might be due to:\n• Corrupted installation files\n• Missing static build files\n• File system permissions\n• Antivirus interference`,
            buttons: ['Retry', 'Open DevTools', 'Quit'],
            defaultId: 0,
            cancelId: 2
          });
          
          switch ((dialogResult as any).response) {
            case 0: // Retry
              console.log('[Electron] 🔄 User requested retry');
              return attemptLoad(0);
            case 1: // Open DevTools
              console.log('[Electron] 🔧 Opening DevTools for debugging');
              mainWindow?.webContents.openDevTools();
              // Try to load a basic error page
              try {
                await mainWindow?.loadURL('data:text/html,<html><body><h1>Application Loading Error</h1><p>Check the DevTools console for details.</p><button onclick="location.reload()">Retry</button></body></html>');
              } catch (basicLoadError) {
                console.error('[Electron] Failed to load even basic error page:', basicLoadError);
              }
              break;
            case 2: // Quit
            default:
              console.log('[Electron] 🚪 User chose to quit');
              app.quit();
              break;
          }
        }
      };
      
      // Start the loading process
      attemptLoad().catch((finalError) => {
        console.error('[Electron] 💥 Final error in static file loading:', finalError);
        // Last resort - try to show something
        mainWindow?.loadURL('data:text/html,<html><body><h1>Critical Error</h1><p>Application failed to start. Please reinstall.</p></body></html>').catch(() => {
          console.error('[Electron] Cannot even load basic HTML - critical failure');
        });
      });
    }
  }

  // Show when ready
  mainWindow.once('ready-to-show', () => {
    if (mainWindow) {
      mainWindow.show();
    }
  });

  // Setup app menu
  setupAppMenu();

  // Open external links in browser
  mainWindow.webContents.setWindowOpenHandler(({ url }: { url: string }) => {
    if (url.startsWith('http')) {
      shell.openExternal(url);
      return { action: 'deny' };
    }
    return { action: 'allow' };
  });

  // Emitted when the window is closed
  mainWindow.on('closed', () => {
    // Dereference the window object
    mainWindow = null;
  });

  if (isDev) {
    setupReloadWatcher(mainWindow);
  }

  // Before loading the URL, set a flag that our app can check
  mainWindow.webContents.executeJavaScript(`
    window.IS_DESKTOP_APP = true;
    window.electron = { isElectron: true };
    console.log('✅ Electron environment variables set');
  `).catch((err: Error) => {
    console.error('Failed to set electron environment variables:', err);
  });
}

function setupAppMenu(): void {
  const template: any[] = [
    {
      label: 'File',
      submenu: [
        { role: 'quit' },
      ],
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
      ],
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' },
      ],
    },
    {
      role: 'help',
      submenu: [
        {
          label: 'Learn More',
          click: async () => {
            await shell.openExternal('https://capacitorjs.com/docs/electron');
          },
        },
        {
          label: 'About Database',
          click: async () => {
            if (mainWindow) {
              const messageBoxOptions = {
                type: 'info' as const,
                title: 'Database Information',
                message: 'Local Database',
                detail: `This application uses PouchDB connected directly to CouchDB for data storage.\n\nDatabase location: ${dbRootPath || 'Not initialized'}`,
                buttons: ['OK']
              };
              
              const { shell } = require('electron');
              const dialogResult = await dialog.showMessageBox(mainWindow, messageBoxOptions);
              const response = (dialogResult as any).response;
              
              // If user clicks OK and path exists, open the folder
              if (response === 0 && dbRootPath && fs.existsSync(dbRootPath)) {
                shell.openPath(dbRootPath);
              }
            }
          }
        },
        {
          label: 'How to Update',
          click: async () => {
            if (mainWindow) {
              const messageBoxOptions = {
                type: 'info' as const,
                title: 'How to Update',
                message: 'Manual Update Required',
                detail: 'To update this app, download the latest version (.dmg or .zip) from your provider, close the app, and replace the old version in your Applications folder. There is no automatic update. Repeat on each device as needed.',
                buttons: ['OK']
              };
              await dialog.showMessageBox(mainWindow, messageBoxOptions);
            }
          }
        },
        {
          label: 'Check for Updates',
          click: async () => {
            if (mainWindow && !isDev) {
              console.log('🔄 Manual update check triggered');
              autoUpdater.checkForUpdates();
            } else if (isDev && mainWindow) {
              dialog.showMessageBox(mainWindow, {
                type: 'info' as const,
                title: 'Development Mode',
                message: 'Auto-updates are disabled in development mode.',
                buttons: ['OK']
              });
            }
          }
        },
      ],
    },
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// Handle logs from renderer process
ipcMain.on('renderer-log', (event: IpcMainEvent, level: string, ...args: any[]) => {
  const levelStr = String(level).toUpperCase();
  // Attempt a slightly more robust stringification for non-string args
  const logArgs = args.map(arg => {
    if (typeof arg === 'string') return arg;
    if (arg instanceof Error) return arg.stack || arg.message; // Show stack for errors
    try {
      return JSON.stringify(arg, null, 2); // Pretty print objects
    } catch (e) {
      return String(arg); // Fallback for unstringifiable objects
    }
  });
  
  switch (levelStr) {
    case 'LOG':
      console.log('[Renderer]', ...logArgs);
      break;
    case 'WARN':
      console.warn('[Renderer WARN]', ...logArgs);
      break;
    case 'ERROR':
      console.error('[Renderer ERROR]', ...logArgs);
      break;
    case 'INFO':
      console.info('[Renderer INFO]', ...logArgs);
      break;
    case 'DEBUG':
      console.debug('[Renderer DEBUG]', ...logArgs);
      break;
    default:
      console.log(`[Renderer ${levelStr}]`, ...logArgs);
  }
});

// Setup app when ready
app.whenReady().then(async () => {
  try {
    // Ensure app directories exist
    ensureAppDirectories();

    // Load or generate device ID
    deviceId = loadOrGenerateDeviceId();
    console.log(`📱 Device ID: ${deviceId}`);

    // Create the main window
    createWindow();
    // Setup database IPC handlers early so renderer ensure-db-opened calls will find a handler
    setupDatabaseIpcHandlers();

    // Initialize CouchDB server
    console.log('[index.ts] Initializing CouchDB server...');
    try {
      const couchdbInitialized = await initCouchDBServer(dbRootPath);
      if (couchdbInitialized) {
        console.log('[index.ts] ✅ CouchDB server started successfully');
        isCouchDBReady = true;
      } else {
        console.error('[index.ts] ❌ Failed to start CouchDB server');
        isCouchDBReady = false;
      }
    } catch (error) {
      console.error('[index.ts] ❌ Error starting CouchDB server:', error);
      isCouchDBReady = false;
    }

    app.on('activate', () => {
      // On macOS it's common to re-create a window in the app when the
      // dock icon is clicked and there are no other windows open.
      if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
      }
    });

    // Auto-update: check for updates on launch
    if (!isDev) {
      console.log('🔄 Checking for updates...');
      autoUpdater.checkForUpdatesAndNotify();
    }

    autoUpdater.on('update-available', (info: any) => {
      console.log('📦 Update available, downloading...', info);
      if (mainWindow) {
        mainWindow.webContents.send('update-available', {
          version: info.version,
          releaseNotes: info.releaseNotes || 'Bug fixes and improvements'
        });
      }
    });

    autoUpdater.on('update-downloaded', (info: any) => {
      console.log('✅ Update downloaded, ready to install', info);
      if (mainWindow) {
        mainWindow.webContents.send('update-downloaded', {
          version: info.version
        });
      }
    });

    autoUpdater.on('update-not-available', () => {
      console.log('✅ App is up to date');
    });

    autoUpdater.on('error', (error: Error) => {
      console.error('❌ Auto-updater error:', error);
      if (mainWindow && !isDev) {
        mainWindow.webContents.send('update-error', {
          message: error ? error.toString() : 'Unknown error occurred'
        });
      }
    });

    autoUpdater.on('checking-for-update', () => {
      console.log('🔍 Checking for update...');
    });

    autoUpdater.on('download-progress', (progressObj: any) => {
      let log_message = "Download speed: " + progressObj.bytesPerSecond;
      log_message = log_message + ' - Downloaded ' + progressObj.percent + '%';
      log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')';
      console.log('📥 Download progress:', log_message);
      
      if (mainWindow) {
        mainWindow.webContents.send('download-progress', {
          percent: progressObj.percent,
          bytesPerSecond: progressObj.bytesPerSecond,
          transferred: progressObj.transferred,
          total: progressObj.total
        });
      }
    });

    // Start USB monitoring safely
    try {
      usbDetect.startMonitoring();
    } catch (error) {
      console.warn('Failed to start USB monitoring:', error);
    }

    

    // IPC handler: List all USB devices (barcode scanners, etc.)
    ipcMain.handle('get-usb-devices', async () => {
      try {
        return await usbDetect.find();
      } catch (error) {
        console.warn('USB detection failed:', error);
        return [];
      }
    });

    // IPC handler: Quit and install update
    ipcMain.handle('quit-and-install', async () => {
      console.log('🔄 Restarting to apply update...');
      autoUpdater.quitAndInstall();
    });

    // 🖨️ IPC handler: Get system printers
    ipcMain.handle('get-system-printers', async () => {
      try {
        // Get system printers using Electron's built-in API
        const printers = await mainWindow?.webContents.getPrintersAsync() || [];
        
        // Define a function to interpret printer status codes
        const getPrinterStatus = (statusCode: number): string => {
          // Simpler interpretation for online/offline/unknown status
          // based on common printer states.

          const PRINTER_ST_OFFLINE = 0x00000080;
          const PRINTER_ST_ERROR = 0x00000002;
          const PRINTER_ST_NOT_AVAILABLE = 0x00001000;
          const PRINTER_ST_PENDING_DELETION = 0x00000004;

          if (statusCode === 0) {
            return 'online'; // Printer is idle/ready
          }
          if (
            (statusCode & PRINTER_ST_OFFLINE) ||
            (statusCode & PRINTER_ST_ERROR) ||
            (statusCode & PRINTER_ST_NOT_AVAILABLE) ||
            (statusCode & PRINTER_ST_PENDING_DELETION)
          ) {
            return 'offline'; // Printer is genuinely offline or in a critical error state
          }
          
          // For any other status codes, we'll categorize as 'unknown'
          return 'unknown';
        };

        // Transform to our format
        const systemPrinters = printers.map((printer: any, index: number) => ({
          id: `system-${printer.name.replace(/\s+/g, '-').toLowerCase()}-${index}`,
          name: printer.name,
          status: getPrinterStatus(printer.status), // Use the new function
          rawStatus: printer.status, // Include raw status for debugging
          type: printer.description?.toLowerCase().includes('thermal') ? 'thermal' : 
                printer.description?.toLowerCase().includes('laser') ? 'laser' : 'inkjet',
          description: printer.description,
          isDefault: printer.isDefault
        }));
        
        console.log(`🖨️ Discovered ${systemPrinters.length} system printers:`, systemPrinters);
        return systemPrinters;
      } catch (error) {
        console.error('Error getting system printers:', error);
        return [];
      }
    });
  } catch (error) {
    console.error('❌ Error during app initialization:', error);
  }
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Clean up resources when quitting
app.on('before-quit', async (event) => {
  console.log('[app] before-quit: Starting cleanup...');
  
  // Prevent immediate quit to allow cleanup
  event.preventDefault();
  
  try {
    // Clean up all syncs
    await cleanupAllLocalSyncs();
    
    // Clean up CouchDB server
    await cleanupCouchDBServer();
    
    console.log('[app] before-quit: Cleanup completed');
  } catch (error) {
    console.error('[app] before-quit: Error during cleanup:', error);
  }
  
  // Now allow the app to quit
  app.quit();
});

// Ensure app directories for database storage exist
function ensureAppDirectories() {
  try {
    // Resolve database directory based on environment
    if (!dbRootPath) {
      const isDev = process.env.NODE_ENV === 'development' && process.env.ELECTRON_FORCE_STATIC !== 'true';
      
      if (isDev) {
        // 🔧 DEV MODE: Use dedicated dev directory in electron folder
        dbRootPath = path.join(app.getAppPath(), 'pouchdb-data');
        console.log('🛠️ Development mode: Using dedicated dev database directory');
      } else {
        // 🚀 PROD MODE: Use userData directory (existing behavior)
        const userDataPath = app.getPath('userData');
        dbRootPath = path.join(userDataPath, 'pouchdb-data');
        console.log('🚀 Production mode: Using userData database directory');
      }
    }

    console.log('📂 Target PouchDB Database directory:', dbRootPath);
    
    // Make sure it exists with proper permissions
    if (!fs.existsSync(dbRootPath)) {
      fs.mkdirSync(dbRootPath, { recursive: true, mode: 0o755 });
      console.log('Created PouchDB database directory:', dbRootPath);
    } else {
      console.log('PouchDB Database directory exists:', dbRootPath);
      
      // Ensure proper permissions on existing directory
      try {
        fs.chmodSync(dbRootPath, 0o755);
      } catch (permErr) {
        console.warn('Could not update permissions on database directory:', permErr);
      }
    }
    
    console.log('✅ App directories configured - CouchDB will store data at:', dbRootPath);
  } catch (err) {
    console.error('Error setting up app directories:', err);
  }
}

/**
 * Load an existing device ID from storage or generate a new one
 */
function loadOrGenerateDeviceId(): string {
  const deviceIdPath = path.join(dbRootPath, 'device-id.txt');
  
  try {
    // Check if the device ID file exists
    if (fs.existsSync(deviceIdPath)) {
      // Load existing device ID
      const storedId = fs.readFileSync(deviceIdPath, 'utf8').trim();
      if (storedId && storedId.length > 10) {
        console.log('✅ Loaded existing device ID');
        return storedId;
      }
    }
    
    // Generate a new device ID
    const newId = uuidv4();
    console.log('📝 Generated new device ID');
    
    // Save to file
    fs.writeFileSync(deviceIdPath, newId);
    
    return newId;
  } catch (error) {
    console.error('❌ Error handling device ID:', error);
    // If all else fails, generate a temporary device ID
    // This won't be persisted but will work for this session
    return `temp-${uuidv4()}`;
  }
}

// --- Database Helper Functions (Main Process) ---

interface DatabaseInstances {
  remoteDb: PouchDB.Database | null;
  localDb: PouchDB.Database | null;
  isRemoteAvailable: boolean;
  lastRemoteCheck: number;
}

const databaseInstances: Map<string, DatabaseInstances> = new Map();
const REMOTE_CHECK_INTERVAL = 60000; // 1 minute

/**
 * Clear cached database instances for a specific restaurant
 * This is critical when switching accounts with different restaurant IDs
 */
function clearCachedDatabaseInstances(restaurantIdPattern?: string): void {
  console.log(`🧹 [clearCachedDatabaseInstances] Clearing cached database instances${restaurantIdPattern ? ` for pattern: ${restaurantIdPattern}` : ''}`);

  if (restaurantIdPattern) {
    // Clear specific restaurant databases
    const keysToDelete: string[] = [];
    for (const [key] of databaseInstances) {
      if (key.includes(restaurantIdPattern)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => {
      const instance = databaseInstances.get(key);
      if (instance?.remoteDb) {
        try {
          // Close the database connection
          if (typeof instance.remoteDb.close === 'function') {
            instance.remoteDb.close();
          }
        } catch (error) {
          console.warn(`⚠️ Error closing database ${key}:`, error);
        }
      }
      databaseInstances.delete(key);
      console.log(`🗑️ Cleared cached database instance: ${key}`);
    });
  } else {
    // Clear all cached instances
    for (const [key, instance] of databaseInstances) {
      if (instance?.remoteDb) {
        try {
          if (typeof instance.remoteDb.close === 'function') {
            instance.remoteDb.close();
          }
        } catch (error) {
          console.warn(`⚠️ Error closing database ${key}:`, error);
        }
      }
    }
    databaseInstances.clear();
    console.log(`🧹 Cleared all cached database instances`);
  }
}

async function initializeDatabaseInstances(dbIdentifier: string): Promise<DatabaseInstances> {
  // knowledge: Guard to prevent duplicate initialization and sync
  if (databaseInstances.has(dbIdentifier)) {
    console.log(`[initializeDatabaseInstances] Guard: Already initialized for ${dbIdentifier}, returning existing instance.`);
    return databaseInstances.get(dbIdentifier)!;
  }

  console.log(`[initializeDatabaseInstances] Setting up CouchDB-only instance for ${dbIdentifier}`);
  
  try {
    // Enhanced CouchDB connection with robust retry logic
    let remoteDb: PouchDB.Database | null = null;
    let isRemoteAvailable = false;
    
    // Use fixed CouchDB port (IP discovery system handles this)
    const port = 5984;
    console.log(`[initializeDatabaseInstances] Using fixed CouchDB port: ${port}`);

    console.log(`[initializeDatabaseInstances] CouchDB port detected: ${port}`);
    
    // Enhanced CouchDB readiness check with multiple validation steps
    const maxRetries = 15;
    const baseDelay = 300;
    let lastError: any = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`[initializeDatabaseInstances] Attempt ${attempt}/${maxRetries} to connect to CouchDB...`);
        
        // Step 1: Test basic CouchDB connectivity
        const healthCheckUrl = `http://localhost:${port}/`;
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 2000);
        
        const healthResponse = await fetch(healthCheckUrl, { 
          signal: controller.signal,
          headers: { 'Accept': 'application/json' }
        });
        clearTimeout(timeoutId);
        
        if (!healthResponse.ok) {
          throw new Error(`CouchDB health check failed: ${healthResponse.status} ${healthResponse.statusText}`);
        }
        
        const healthData = await healthResponse.json();
        console.log(`[initializeDatabaseInstances] CouchDB health check passed:`, healthData);
        
        // Step 2: Test admin authentication
        const couchDbUrl = `****************************:${port}`;
        const nanoModule = require('nano');
        const nanoDb = nanoModule(couchDbUrl);
        
        // Step 3: Verify we can list databases (admin access test)
        const dbList = await nanoDb.db.list();
        console.log(`[initializeDatabaseInstances] CouchDB admin access verified. Existing databases:`, dbList.length);
        
        // Step 4: Ensure the specific database exists
        if (!dbList.includes(dbIdentifier)) {
          console.log(`[initializeDatabaseInstances] Creating CouchDB database '${dbIdentifier}'...`);
          await nanoDb.db.create(dbIdentifier);
          console.log(`[initializeDatabaseInstances] ✅ Database '${dbIdentifier}' created successfully`);
        } else {
          console.log(`[initializeDatabaseInstances] ✅ Database '${dbIdentifier}' already exists`);
        }
        
        // Step 5: Create and test PouchDB connection
        const remoteDbUrl = `****************************:${port}/${dbIdentifier}`;
        remoteDb = new PouchDBConstructor(remoteDbUrl);
        
        // Step 6: Test actual database operations
        const dbInfo = await remoteDb.info();
        console.log(`[initializeDatabaseInstances] ✅ PouchDB connection established. DB info:`, {
          db_name: dbInfo.db_name,
          doc_count: dbInfo.doc_count,
          update_seq: dbInfo.update_seq
        });
        
        isRemoteAvailable = true;
        console.log(`[initializeDatabaseInstances] ✅ CouchDB instance fully validated for ${dbIdentifier} at port ${port}`);
        break;
        
      } catch (error: any) {
        lastError = error;
        console.warn(`[initializeDatabaseInstances] Attempt ${attempt}/${maxRetries} failed for ${dbIdentifier}:`, error.message);
        
        if (attempt === maxRetries) {
          console.error(`[initializeDatabaseInstances] ❌ All ${maxRetries} attempts failed for ${dbIdentifier}`);
          break;
        }
        
        // Exponential backoff with jitter
        const delay = baseDelay * Math.pow(1.5, attempt - 1) + Math.random() * 100;
        console.log(`[initializeDatabaseInstances] Waiting ${Math.round(delay)}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    if (!isRemoteAvailable || !remoteDb) {
      const errorMsg = lastError ? lastError.message : 'Unknown error';
      throw new Error(`Failed to connect to CouchDB for ${dbIdentifier} after ${maxRetries} attempts. Last error: ${errorMsg}`);
    }

    const instances: DatabaseInstances = {
      remoteDb,
      localDb: remoteDb, // Use CouchDB as both remote and local
      isRemoteAvailable,
      lastRemoteCheck: Date.now()
    };

    // No sync supervision needed since we're using CouchDB directly
    console.log(`[initializeDatabaseInstances] ✅ Using CouchDB directly, no sync supervision needed for ${dbIdentifier}`);

    databaseInstances.set(dbIdentifier, instances);
    console.log(`[initializeDatabaseInstances] ✅ Successfully initialized ${dbIdentifier} (CouchDB-only architecture)`);
    return instances;
    
  } catch (error: any) {
    console.error(`[initializeDatabaseInstances] ❌ Error setting up instances for ${dbIdentifier}:`, error);
    throw error;
  }
}

async function getDatabaseInstances(dbIdentifier: string): Promise<DatabaseInstances> {
  if (databaseInstances.has(dbIdentifier)) {
    const instances = databaseInstances.get(dbIdentifier)!;
    
    // Check if we should test remote availability again
    const now = Date.now();
    if (!instances.isRemoteAvailable && (now - instances.lastRemoteCheck) > REMOTE_CHECK_INTERVAL) {
      instances.lastRemoteCheck = now;
      
      // Try to reconnect to remote
      if (instances.remoteDb) {
        try {
          await instances.remoteDb.info();
          instances.isRemoteAvailable = true;
          console.log(`[getDatabaseInstances] Remote reconnected for ${dbIdentifier}`);
        } catch (err) {
          console.log(`[getDatabaseInstances] Remote still unavailable for ${dbIdentifier}`);
        }
      }
    }
    
    return instances;
  }

  return await initializeDatabaseInstances(dbIdentifier);
}

// Simplified database operations - sync supervision handles real-time sync
async function getDocumentSimple(dbIdentifier: string, docId: string, options?: PouchDB.Core.GetOptions): Promise<any> {
  const instances = await getDatabaseInstances(dbIdentifier);
  
  // In CouchDB-only architecture, use the CouchDB instance directly
  const db = instances.remoteDb || instances.localDb;
  
  if (!db) {
    throw new Error(`No database available for ${dbIdentifier}`);
  }
  
  try {
    // Fix for PouchDB HTTP adapter bug: Remove problematic revs parameter
    const cleanOptions = options ? { ...options } : {};
    delete cleanOptions.revs; // This causes "Cannot read properties of undefined (reading 'revs')" error
    delete cleanOptions.revs_info; // Also problematic with HTTP adapter
    
    // Enhanced error handling for document retrieval
    const result = await db.get(docId, cleanOptions);
    return result;
  } catch (error: any) {
    // Handle common CouchDB errors gracefully
    if (error.status === 404 || error.name === 'not_found') {
      throw error; // Re-throw 404 as expected
    }
    
    // For other errors, add more context
    console.error(`[getDocumentSimple] Error getting document ${docId} from ${dbIdentifier}:`, error);
    throw error;
  }
}

async function putDocumentSimple(dbIdentifier: string, doc: PouchDB.Core.PutDocument<any>): Promise<any> {
  const instances = await getDatabaseInstances(dbIdentifier);
  
  // In CouchDB-only architecture, use the CouchDB instance directly
  const db = instances.remoteDb || instances.localDb;
  
  if (!db) {
    throw new Error(`No database available for ${dbIdentifier}`);
  }
  
  try {
    // Enhanced conflict handling for document updates
    const result = await db.put(doc);
    return result;
  } catch (error: any) {
    // Handle document conflicts gracefully
    if (error.status === 409 || error.name === 'conflict') {
      console.warn(`[putDocumentSimple] Document conflict for ${doc._id} in ${dbIdentifier}, caller should handle retry`);
      throw error; // Re-throw conflict for caller to handle
    }
    
    // For other errors, add more context
    console.error(`[putDocumentSimple] Error putting document ${doc._id} to ${dbIdentifier}:`, error);
    throw error;
  }
}

async function removeDocumentSimple(dbIdentifier: string, docId: string, rev: string): Promise<any> {
  const instances = await getDatabaseInstances(dbIdentifier);
  
  // In CouchDB-only architecture, use the CouchDB instance directly
  const db = instances.remoteDb || instances.localDb;
  
  if (!db) {
    throw new Error(`No database available for ${dbIdentifier}`);
  }
  
  return db.remove(docId, rev);
}

async function bulkDocsSimple(dbIdentifier: string, docsParam: Array<any> | { docs: Array<any> }, options?: PouchDB.Core.BulkDocsOptions): Promise<any> {
  const instances = await getDatabaseInstances(dbIdentifier);
  
  // In CouchDB-only architecture, use the CouchDB instance directly
  const db = instances.remoteDb || instances.localDb;
  
  if (!db) {
    throw new Error(`No database available for ${dbIdentifier}`);
  }
  
  // Ensure we pass the correct format to PouchDB
  const docs = Array.isArray(docsParam) ? docsParam : docsParam.docs;
  return db.bulkDocs(docs, options);
}

async function findDocumentsSimple(dbIdentifier: string, findRequest: PouchDB.Find.FindRequest<any>): Promise<any> {
  console.log(`[findDocumentsSimple] Starting find operation for ${dbIdentifier}`);
  const instances = await getDatabaseInstances(dbIdentifier);
  
  // In CouchDB-only architecture, use the CouchDB instance directly
  const db = instances.remoteDb || instances.localDb;
  
  if (!db) {
    console.error(`[findDocumentsSimple] No local database available for ${dbIdentifier}`);
    throw new Error(`No database available for ${dbIdentifier}`);
  }
  
  // Test database connectivity
  try {
    await db.info();
    console.log(`[findDocumentsSimple] Database ${dbIdentifier} is accessible`);
  } catch (infoError: any) {
    console.error(`[findDocumentsSimple] Database ${dbIdentifier} info check failed:`, infoError);
    throw new Error(`Database ${dbIdentifier} is not accessible: ${infoError.message}`);
  }
  
  console.log(`[findDocumentsSimple] Using local DB for ${dbIdentifier}, about to call db.find`);
  console.log(`[findDocumentsSimple] Find request:`, JSON.stringify(findRequest, null, 2));
  
  try {
    const result = await db.find(findRequest);
    console.log(`[findDocumentsSimple] Find operation successful, returned ${result?.docs?.length || 0} documents`);
    return result;
  } catch (error: any) {
    console.error(`[findDocumentsSimple] Error in db.find for ${dbIdentifier}:`, error);
    console.error(`[findDocumentsSimple] Error details - status: ${error.status}, name: ${error.name}, message: '${error.message}'`);
    
    // If it's a sort/index error, try a simpler query
    if (error.status === 400 || error.message?.includes('sort') || error.message?.includes('index')) {
      console.warn(`[findDocumentsSimple] Attempting fallback query without sort for ${dbIdentifier}`);
      try {
        const fallbackRequest = { ...findRequest };
        delete fallbackRequest.sort;
        delete fallbackRequest.use_index;
        const fallbackResult = await db.find(fallbackRequest);
        console.log(`[findDocumentsSimple] Fallback query successful, returned ${fallbackResult?.docs?.length || 0} documents`);
        return fallbackResult;
      } catch (fallbackError: any) {
        console.error(`[findDocumentsSimple] Fallback query also failed:`, fallbackError);
      }
    }
    
    throw error;
  }
}

async function createIndexSimple(dbIdentifier: string, indexDefinition: PouchDB.Find.CreateIndexOptions): Promise<any> {
  const instances = await getDatabaseInstances(dbIdentifier);
  
  // In CouchDB-only architecture, use the CouchDB instance directly
  const db = instances.remoteDb || instances.localDb;
  
  if (!db) {
    throw new Error(`No database available for ${dbIdentifier}`);
  }
  
  try {
    const result = await db.createIndex(indexDefinition);
    return result;
  } catch (error: any) {
    console.warn(`[createIndexSimple] Error creating index on CouchDB for ${dbIdentifier}:`, error);
    throw error;
  }
}

// Legacy function for backward compatibility - now just initializes instances
async function getPouchDBInstance(dbIdentifier: string): Promise<PouchDB.Database> {
  const instances = await getDatabaseInstances(dbIdentifier);
  
  // Return the remote instance if available, otherwise local
  if (instances.isRemoteAvailable && instances.remoteDb) {
    return instances.remoteDb;
  }
  
  if (!instances.localDb) {
    throw new Error(`No database instances available for ${dbIdentifier}`);
  }
  
  return instances.localDb;
}

async function closePouchDB(dbIdentifier: string): Promise<void> {
  if (databaseInstances.has(dbIdentifier)) {
    const instances = databaseInstances.get(dbIdentifier)!;
    
    try {
      // Close CouchDB connection
      if (instances.remoteDb) {
        await instances.remoteDb.close();
      }
      
      databaseInstances.delete(dbIdentifier);
      console.log(`[closePouchDB] CouchDB instance closed for ${dbIdentifier}`);
    } catch (error) {
      console.error(`[closePouchDB] Error closing instances for ${dbIdentifier}:`, error);
      databaseInstances.delete(dbIdentifier); // Still remove from map
      throw error;
    }
  } else {
    console.log(`[closePouchDB] No instances found for ${dbIdentifier}`);
  }
}

async function destroyPouchDB(dbIdentifier: string): Promise<void> {
  const instances = databaseInstances.get(dbIdentifier);

  if (instances) {
    try {
      // Destroy CouchDB database
      if (instances.remoteDb) {
        try {
          await instances.remoteDb.destroy();
          console.log(`[destroyPouchDB] CouchDB instance destroyed for ${dbIdentifier}`);
        } catch (remoteError) {
          console.warn(`[destroyPouchDB] Could not destroy CouchDB instance for ${dbIdentifier}:`, remoteError);
        }
      }
      
      databaseInstances.delete(dbIdentifier);
    } catch (error) {
      console.error(`[destroyPouchDB] Error destroying instances for ${dbIdentifier}:`, error);
      databaseInstances.delete(dbIdentifier); // Still remove from map
      throw error;
    }
  } else {
    console.log(`[destroyPouchDB] No cached instances for ${dbIdentifier}.`);
  }
}

// --- Setup Database IPC Handlers --- 
function setupDatabaseIpcHandlers() {
  if (dbIpcHandlersRegistered) {
    console.log('[index.ts] Main: DB IPC handlers already registered, skipping.');
    return;
  }
  dbIpcHandlersRegistered = true;
  console.log('[index.ts] Main: Setting up PouchDB IPC handlers...');

  // New handler to get the list of available databases
  ipcMain.handle('get-database-list', async (event: IpcMainInvokeEvent) => {
    try {
      // List all directories in the dbRootPath as these are the databases
      const files = fs.readdirSync(dbRootPath);
      
      // Filter to include only directories (PouchDB databases)
      const databases = files.filter(file => {
        const fullPath = path.join(dbRootPath, file);
        // Skip device-id.txt and any hidden files
        if (file === 'device-id.txt' || file.startsWith('.')) {
          return false;
        }
        // Only include directories that are potentially PouchDB databases
        return fs.statSync(fullPath).isDirectory();
      });
      
      console.log(`Found ${databases.length} PouchDB databases in ${dbRootPath}`);
      return { ok: true, data: databases };
    } catch (error) {
      console.error('Error listing databases:', error);
      return { ok: false, error: String(error) };
    }
  });

  // Handler to ensure a database is opened/initialized via PouchDB
  console.log('[index.ts] Main: Attempting to register handler for ensure-db-opened');
  ipcMain.handle('ensure-db-opened', async (event: IpcMainInvokeEvent, dbIdentifier: string) => {
    console.log(`[index.ts] IPC: Received ensure-db-opened for ${dbIdentifier}`);
    if (!dbIdentifier) {
      return { ok: false, error: { status: 400, name: 'bad_request', message: 'Missing dbIdentifier' } };
    }
    
    // Retry logic for CouchDB readiness
    const maxRetries = 10;
    const baseDelay = 500; // 500ms base delay
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Check if CouchDB is ready before attempting database operations
        if (!isCouchDBReady) {
          throw new Error('CouchDB is not ready yet - P2P sync initialization may still be in progress');
        }
        
        await getDatabaseInstances(dbIdentifier); // This will create/initialize both instances and start background sync
        console.log(`[index.ts] IPC: ensure-db-opened for ${dbIdentifier} - SUCCESS on attempt ${attempt}`);
        
        return { ok: true, message: `Database instances ${dbIdentifier} ensured with CouchDB-first architecture.` };
      } catch (error: any) {
        console.warn(`[index.ts] Main: Attempt ${attempt}/${maxRetries} failed for ${dbIdentifier}:`, error.message);
        
        // If this is the last attempt, return the error
        if (attempt === maxRetries) {
          console.error(`[index.ts] Main: All ${maxRetries} attempts failed for ${dbIdentifier}:`, error);
          return { ok: false, error: { status: error.status || 500, name: error.name || 'db_open_error', message: error.message } };
        }
        
        // Wait before retrying (exponential backoff)
        const delay = baseDelay * Math.pow(2, attempt - 1);
        console.log(`[index.ts] Waiting ${delay}ms before retry ${attempt + 1}...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  });
  console.log('[index.ts] Main: Handler for ensure-db-opened should be registered.');

  ipcMain.handle('pouchdb-get', async (event: IpcMainInvokeEvent, dbIdentifier: string, docId: string, options?: PouchDB.Core.GetOptions) => {
    console.log(`[index.ts] IPC: Received pouchdb-get for doc '${docId}' in '${dbIdentifier}'`);
    if (!dbIdentifier || !docId) {
      return { ok: false, error: { status: 400, name: 'bad_request', message: 'Missing dbIdentifier or docId' } };
    }
    try {
      // Clean options to prevent PouchDB HTTP adapter bugs
      const cleanOptions = options ? { ...options } : {};
      delete cleanOptions.revs; // Causes "Cannot read properties of undefined (reading 'revs')" error
      delete cleanOptions.revs_info; // Also problematic with HTTP adapter
      
      const doc = await getDocumentSimple(dbIdentifier, docId, cleanOptions);
      return { ok: true, data: doc };
    } catch (error: any) {
      console.error(`Main: Error in pouchdb-get for doc '${docId}' in '${dbIdentifier}':`, error);
      return { ok: false, error: { status: error.status || 404, name: error.name || 'not_found', message: error.message } };
    }
  });

  ipcMain.handle('pouchdb-put', async (event: IpcMainInvokeEvent, dbIdentifier: string, doc: PouchDB.Core.PutDocument<any>) => {
    console.log(`[index.ts] IPC: Received pouchdb-put for doc '${doc._id}' in '${dbIdentifier}'`);
    if (!dbIdentifier || !doc || !doc._id) {
      return { ok: false, error: { status: 400, name: 'bad_request', message: 'Missing dbIdentifier or document with _id' } };
    }
    try {
      const response = await putDocumentSimple(dbIdentifier, doc);
      return { ok: true, data: response };
    } catch (error: any) {
      console.error(`Main: Error in pouchdb-put for doc '${doc._id}' in '${dbIdentifier}':`, error);
      return { ok: false, error: { status: error.status || 500, name: error.name || 'put_error', message: error.message } };
    }
  });

  ipcMain.handle('pouchdb-remove', async (event: IpcMainInvokeEvent, dbIdentifier: string, docId: string, rev: string) => {
    console.log(`[index.ts] IPC: Received pouchdb-remove for doc '${docId}' in '${dbIdentifier}'`);
    if (!dbIdentifier || !docId || !rev) {
      // Forcing rev to be passed. If it's not available, the caller should fetch the doc first.
      return { ok: false, error: { status: 400, name: 'bad_request', message: 'Missing dbIdentifier, docId, or docRev for remove' } };
    }
    try {
      const response = await removeDocumentSimple(dbIdentifier, docId, rev);
      return { ok: true, data: response };
    } catch (error: any) {
      console.error(`Main: Error in pouchdb-remove for doc '${docId}' in '${dbIdentifier}':`, error);
      return { ok: false, error: { status: error.status || 500, name: error.name || 'remove_error', message: error.message } };
    }
  });

  ipcMain.handle('pouchdb-bulk-docs', async (event: IpcMainInvokeEvent, dbIdentifier: string, docsParam: Array<any> | { docs: Array<any> }, options?: PouchDB.Core.BulkDocsOptions) => {
    const numDocs = Array.isArray(docsParam) ? docsParam.length : docsParam.docs.length;
    console.log(`[index.ts] IPC: Received pouchdb-bulk-docs for '${dbIdentifier}' with ${numDocs} docs`);
    if (!dbIdentifier || !docsParam) {
      return { ok: false, error: { status: 400, name: 'bad_request', message: 'Missing dbIdentifier or docsParam' } };
    }
    try {
      const response = await bulkDocsSimple(dbIdentifier, docsParam, options);
      return { ok: true, data: response };
    } catch (error: any) {
      console.error(`Main: Error in pouchdb-bulk-docs for '${dbIdentifier}':`, error);
      return { ok: false, error: { status: error.status || 500, name: error.name || 'bulk_docs_error', message: error.message } };
    }
  });

  ipcMain.handle('pouchdb-create-index', async (event: IpcMainInvokeEvent, dbIdentifier: string, indexDef: PouchDB.Find.CreateIndexOptions) => {
    console.log(`[index.ts] IPC: Received pouchdb-create-index for '${dbIdentifier}' with index:`, JSON.stringify(indexDef));

    // Enhanced validation for indexDef
    if (
      !indexDef ||
      typeof indexDef.index !== 'object' ||
      indexDef.index === null ||
      !Array.isArray(indexDef.index.fields) ||
      indexDef.index.fields.length === 0
    ) {
      const errorMessage = 'Invalid index definition from renderer: indexDef must be an object with an \'index\' property, which must be an object containing a non-empty \'fields\' array.';
      console.error(`Main: Invalid indexDef received for pouchdb-create-index for db '${dbIdentifier}': ${errorMessage}`, indexDef);
      return { ok: false, error: { status: 400, name: 'bad_request', message: errorMessage } };
    }

    try {
      const response = await createIndexSimple(dbIdentifier, indexDef);
      return { ok: true, data: response };
    } catch (error: any) {
      console.error(`Main: Error in pouchdb-create-index for '${dbIdentifier}':`, error);
      // Index already exists is a common "error" (status 409) which might be okay for the caller
      return { ok: false, error: { status: error.status || 500, name: error.name || 'index_error', message: error.message } };
    }
  });

  ipcMain.handle('pouchdb-find', async (event: IpcMainInvokeEvent, dbIdentifier: string, findRequest: PouchDB.Find.FindRequest<any>) => {
    console.log(`[index.ts] IPC: Received pouchdb-find for '${dbIdentifier}' with selector:`, JSON.stringify(findRequest.selector));
    if (!dbIdentifier || !findRequest) {
      return { ok: false, error: { status: 400, name: 'bad_request', message: 'Missing dbIdentifier or findRequest' } };
    }
    try {
      const result = await findDocumentsSimple(dbIdentifier, findRequest);
      return { ok: true, data: result };
    } catch (error: any) {
      console.error(`Main: Error in pouchdb-find for '${dbIdentifier}':`, error);
      return { ok: false, error: { status: error.status || 500, name: error.name || 'find_error', message: error.message } };
    }
  });

  ipcMain.handle('pouchdb-close', async (event: IpcMainInvokeEvent, dbIdentifier: string) => {
    console.log(`[index.ts] IPC: Received pouchdb-close for '${dbIdentifier}'`);
    if (!dbIdentifier) {
      return { ok: false, error: { status: 400, name: 'bad_request', message: 'Missing dbIdentifier' } };
    }
    try {
      await closePouchDB(dbIdentifier);
      return { ok: true, message: `Database ${dbIdentifier} closed successfully` };
    } catch (error: any) {
      console.error(`Main: Error in pouchdb-close for '${dbIdentifier}':`, error);
      return { ok: false, error: { status: error.status || 500, name: error.name || 'close_error', message: error.message } };
    }
  });

  ipcMain.handle('pouchdb-destroy', async (event: IpcMainInvokeEvent, dbIdentifier: string) => {
    console.log(`[index.ts] IPC: Received pouchdb-destroy for '${dbIdentifier}'`);
    if (!dbIdentifier) {
      return { ok: false, error: { status: 400, name: 'bad_request', message: 'Missing dbIdentifier' } };
    }
    try {
      await destroyPouchDB(dbIdentifier);
      return { ok: true, message: `Database ${dbIdentifier} destroyed successfully` };
    } catch (error: any) {
      console.error(`Main: Error in pouchdb-destroy for '${dbIdentifier}':`, error);
      return { ok: false, error: { status: error.status || 500, name: error.name || 'destroy_error', message: error.message } };
    }
  });

  // IPC handler for clearing cached database instances (for account switching)
  ipcMain.handle('database:clear-cache', async (event: IpcMainInvokeEvent, restaurantIdPattern?: string) => {
    try {
      console.log(`🧹 [IPC] Clearing database cache${restaurantIdPattern ? ` for pattern: ${restaurantIdPattern}` : ''}`);
      clearCachedDatabaseInstances(restaurantIdPattern);
      return { success: true, message: 'Database cache cleared successfully' };
    } catch (error: any) {
      console.error('[database:clear-cache] Error:', error);
      return { success: false, error: error.message };
    }
  });

  console.log('[index.ts] Main: All PouchDB IPC handlers registered successfully');
}

// --- Sync Management Functions ---

async function cleanupAllLocalSyncs(): Promise<void> {
  console.log('[index.ts] Cleaning up all local syncs...');
  // Since we're using CouchDB directly, no sync cleanup needed
  console.log('[index.ts] ✅ Sync cleanup completed (CouchDB-only architecture)');
}

async function autoSyncExistingDatabases(): Promise<void> {
  console.log('[index.ts] Auto-syncing existing databases...');
  // Since we're using CouchDB directly, no additional sync setup needed
  console.log('[index.ts] ✅ Auto-sync completed (CouchDB-only architecture)');
}