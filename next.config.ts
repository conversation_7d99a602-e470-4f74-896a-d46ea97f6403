import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  productionBrowserSourceMaps: false,
  reactStrictMode: false,
  
  // Conditionally apply static export only for electron/static/mobile builds
  ...(process.env.BUILD_TARGET === 'electron' || 
      process.env.BUILD_TARGET === 'static' || 
      process.env.BUILD_TARGET === 'mobile' ? {
    output: 'export',
    trailingSlash: true,
    images: { unoptimized: true },
  } : {
    images: { domains: ['res.cloudinary.com'] },
  }),
  
  webpack: (config, { isServer, dev }) => {
    // Set relative public path only for static builds
    if (!isServer && !dev && 
        (process.env.BUILD_TARGET === 'electron' || 
         process.env.BUILD_TARGET === 'static' || 
         process.env.BUILD_TARGET === 'mobile')) {
      config.output.publicPath = './_next/';
    }
    
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
        stream: false,
        net: false,
        tls: false,
        child_process: false,
      };
    }

    config.resolve.alias = {
      ...config.resolve.alias,
      '@capacitor/http': require.resolve('./lib/services/empty-capacitor-http.js'),
    };

    if (process.env.BUILD_TARGET === 'web') {
      config.resolve.alias = {
        ...config.resolve.alias,
        '@/lib/services/kitchen-print-service': require.resolve('./lib/services/empty-service.js'),
        '@/lib/services/barcode-service': require.resolve('./lib/services/empty-service.js'),
        '@/lib/services/print-service': require.resolve('./lib/services/empty-service.js'),
        '@/lib/db/electron-db': require.resolve('./lib/services/empty-service.js'),
        '@/lib/auth/mongo-auth-ops': require.resolve('./lib/services/empty-auth-ops.js'),
        '@/lib/mongodb': require.resolve('./lib/services/empty-mongodb.js'),
        '@/app/hooks/use-p2p-sync': require.resolve('./lib/services/empty-hook.ts'),
        '@/app/components/MDNSBrowserComponent': require.resolve('./lib/services/empty-component.tsx'),
      };
    }
    
    return config;
  },
  
  typescript: { ignoreBuildErrors: true },
  eslint: { ignoreDuringBuilds: true },
};

export default nextConfig;