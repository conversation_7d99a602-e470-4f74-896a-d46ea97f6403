/**
 * Static Export Runtime Fix
 * 
 * Critical runtime fixes for static exports to prevent blank screens.
 * This script must be loaded before any other Next.js scripts.
 */

(function() {
  'use strict';
  
  // Only apply fixes in browser environment for static exports
  if (typeof window === 'undefined') return;
  
  console.log('[Static Export] Applying runtime fixes...');
  
  // 1. Fix webpack public path - CRITICAL for chunk loading
  if (typeof __webpack_require__ !== 'undefined') {
    __webpack_require__.p = './_next/';
    console.log('[Static Export] Set webpack public path to ./_next/');
  }
  
  // 2. Fix dynamic import loading - CRITICAL for code splitting
  if (typeof __webpack_require__ !== 'undefined' && typeof __webpack_require__.l === 'function') {
    const originalLoad = __webpack_require__.l;
    __webpack_require__.l = function(url, done, key, chunkId) {
      const originalUrl = url;
      
      // Convert absolute paths to relative
      if (!url.startsWith('./') && !url.startsWith('http')) {
        if (url.startsWith('/_next/')) {
          url = '.' + url;
        } else if (url.startsWith('/')) {
          url = '.' + url;
        }
      }
      
      if (originalUrl !== url) {
        console.log('[Static Export] Corrected chunk URL:', originalUrl, '->', url);
      }
      
      return originalLoad.call(this, url, done, key, chunkId);
    };
  }
  
  // 3. Fix chunk loading with retry mechanism - CRITICAL for reliability
  if (typeof __webpack_require__ !== 'undefined' && typeof __webpack_require__.e === 'function') {
    const originalEnsure = __webpack_require__.e;
    __webpack_require__.e = function(chunkId) {
      console.log('[Static Export] Loading chunk:', chunkId);
      
      return originalEnsure.call(this, chunkId).catch(function(error) {
        console.warn('[Static Export] Chunk loading failed, retrying:', chunkId, error.message);
        
        // Try loading with corrected path after a short delay
        return new Promise(function(resolve, reject) {
          setTimeout(function() {
            originalEnsure.call(this, chunkId)
              .then(resolve)
              .catch(function(retryError) {
                console.error('[Static Export] Chunk retry failed:', chunkId, retryError.message);
                reject(retryError);
              });
          }.bind(this), 100);
        }.bind(this));
      }.bind(this));
    };
  }
  
  // 4. Fix RSC payload loading - CRITICAL for React Server Components
  if (typeof window.fetch === 'function') {
    const originalFetch = window.fetch;
    window.fetch = function(input, init) {
      let url = typeof input === 'string' ? input : input.url;
      const originalUrl = url;
      
      // Fix RSC payload paths
      if (url.startsWith('/_next/') && !url.startsWith('./')) {
        url = '.' + url;
        
        if (typeof input === 'string') {
          input = url;
        } else {
          input = new Request(url, input);
        }
        
        console.log('[Static Export] Corrected RSC URL:', originalUrl, '->', url);
      }
      
      return originalFetch.call(this, input, init).catch(function(error) {
        console.warn('[Static Export] Fetch failed:', originalUrl, error.message);
        
        // Try alternative path resolution
        let fallbackUrl = originalUrl;
        if (fallbackUrl.startsWith('/') && !fallbackUrl.startsWith('./')) {
          fallbackUrl = '.' + fallbackUrl;
          
          const fallbackInput = typeof input === 'string' ? fallbackUrl : new Request(fallbackUrl, input);
          return originalFetch.call(this, fallbackInput, init);
        }
        
        throw error;
      });
    };
  }
  
  // 5. Fix chunk script filename generation
  if (typeof __webpack_require__ !== 'undefined' && typeof __webpack_require__.u === 'function') {
    const originalGetChunkScriptFilename = __webpack_require__.u;
    __webpack_require__.u = function(chunkId) {
      const filename = originalGetChunkScriptFilename.call(this, chunkId);
      
      // Ensure chunk filenames are relative for static exports
      if (filename.startsWith('/') && !filename.startsWith('./')) {
        const correctedFilename = '.' + filename;
        console.log('[Static Export] Corrected chunk filename:', filename, '->', correctedFilename);
        return correctedFilename;
      }
      
      return filename;
    };
  }
  
  // 6. Fix Next.js buildManifest paths
  if (typeof window.__NEXT_DATA__ !== 'undefined' && window.__NEXT_DATA__.buildManifest) {
    console.log('[Static Export] Fixing Next.js buildManifest paths');
    
    const manifest = window.__NEXT_DATA__.buildManifest;
    
    // Fix pages paths
    if (manifest.pages) {
      Object.keys(manifest.pages).forEach(function(page) {
        if (Array.isArray(manifest.pages[page])) {
          manifest.pages[page] = manifest.pages[page].map(function(path) {
            if (path.startsWith('/') && !path.startsWith('./')) {
              return '.' + path;
            }
            return path;
          });
        }
      });
    }
    
    // Fix low priority files
    if (manifest.lowPriorityFiles) {
      manifest.lowPriorityFiles = manifest.lowPriorityFiles.map(function(path) {
        if (path.startsWith('/') && !path.startsWith('./')) {
          return '.' + path;
        }
        return path;
      });
    }
  }
  
  // 7. Monitor for loading errors and provide helpful debugging
  window.addEventListener('error', function(event) {
    if (event.filename && (event.filename.includes('_next') || event.filename.includes('chunks'))) {
      console.error('[Static Export] Asset loading error:', {
        filename: event.filename,
        message: event.message,
        lineno: event.lineno,
        colno: event.colno
      });
      
      console.error('💡 This might be due to incorrect asset paths in static export.');
      console.error('💡 Check that all assets use relative paths starting with "./"');
    }
  });
  
  // 8. Monitor for hydration errors
  window.addEventListener('error', function(event) {
    if (event.message && event.message.includes('Hydration')) {
      console.warn('[Static Export] Hydration error detected:', event.message);
      console.warn('💡 This may cause blank screens - check React component consistency');
    }
  });
  
  console.log('[Static Export] Runtime fixes applied successfully');
  
})();