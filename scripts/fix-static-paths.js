#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function fixPathsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Fix absolute paths to relative paths in HTML and JS files
    const replacements = [
      // Fix CSS paths in inline JavaScript
      [/"\/(_next\/static\/css\/[^"]+)"/g, '"./$1"'],
      // Fix JS chunk paths in inline JavaScript  
      [/"\/(_next\/static\/chunks\/[^"]+)"/g, '"./$1"'],
      // Fix other _next paths
      [/"\/(_next\/[^"]+)"/g, '"./$1"'],
      // Fix href attributes
      [/href="\/(_next\/[^"]+)"/g, 'href="./$1"'],
      // Fix src attributes
      [/src="\/(_next\/[^"]+)"/g, 'src="./$1"'],
    ];
    
    for (const [pattern, replacement] of replacements) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed paths in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function processDirectory(dirPath) {
  let totalFixed = 0;
  
  function walkDir(currentPath) {
    const items = fs.readdirSync(currentPath);
    
    for (const item of items) {
      const itemPath = path.join(currentPath, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        walkDir(itemPath);
      } else if (stat.isFile()) {
        const ext = path.extname(item).toLowerCase();
        if (['.html', '.js', '.css'].includes(ext)) {
          if (fixPathsInFile(itemPath)) {
            totalFixed++;
          }
        }
      }
    }
  }
  
  if (fs.existsSync(dirPath)) {
    console.log(`🔧 Processing directory: ${dirPath}`);
    walkDir(dirPath);
    console.log(`✅ Fixed paths in ${totalFixed} files`);
  } else {
    console.error(`❌ Directory not found: ${dirPath}`);
  }
}

// Process the electron app directory
const electronAppDir = path.join(__dirname, '..', 'electron', 'app');
processDirectory(electronAppDir);
