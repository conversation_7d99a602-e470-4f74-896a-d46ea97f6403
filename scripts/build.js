#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Get build target from command line args
const target = process.argv[2] || 'web';

console.log(`🚀 Building for target: ${target}`);

// Set environment variables based on target
const env = { ...process.env };

switch (target) {
  case 'static':
    env.BUILD_TARGET = 'static';
    env.NODE_ENV = 'production';
    console.log('📦 Static build mode - Generating full static export for offline-first operation');
    break;
  case 'electron':
    env.BUILD_TARGET = 'electron';
    env.NODE_ENV = 'production';
    console.log('🖥️  Electron build mode - Static export enabled with electron-optimized settings');
    break;
  case 'mobile':
    env.BUILD_TARGET = 'mobile';
    env.NODE_ENV = 'production';
    console.log('📱 Mobile build mode - Capacitor-optimized build');
    break;
  case 'web':
    env.BUILD_TARGET = 'web';
    env.NODE_ENV = 'production';
    console.log('🌐 Web build mode - Landing page only, restaurant functionality excluded');
    break;
  case 'server':
  default:
    env.BUILD_TARGET = 'server';
    env.NODE_ENV = 'production';
    console.log('🌐 Server build mode - Full server-side rendering with API routes');
    break;
}

// Clean previous builds
function cleanPreviousBuilds() {
  console.log('🧹 Cleaning previous builds...');
  
  const dirsToClean = ['.next', 'out'];
  if (target === 'electron') {
    dirsToClean.push('electron/app', 'electron/dist');
  }
  
  for (const dir of dirsToClean) {
    const dirPath = path.join(__dirname, '..', dir);
    if (fs.existsSync(dirPath)) {
      console.log(`  Removing ${dir}...`);
      fs.rmSync(dirPath, { recursive: true, force: true });
    }
  }
  
  console.log('✅ Previous builds cleaned');
}

// Validate static export completeness
function validateStaticExport() {
  console.log('🔍 Validating static export completeness...');
  
  const outDir = path.join(__dirname, '..', 'out');
  if (!fs.existsSync(outDir)) {
    throw new Error('❌ Static export failed: out/ directory not found');
  }
  
  // Check for essential files
  const essentialFiles = ['index.html', '_next'];
  for (const file of essentialFiles) {
    const filePath = path.join(outDir, file);
    if (!fs.existsSync(filePath)) {
      console.warn(`⚠️  Missing essential file/directory: ${file}`);
    }
  }
  
  console.log('✅ Static export validation completed');
}

// Hide API directory for static builds
function hideApiDirectory() {
  if (target === 'static' || target === 'electron' || target === 'mobile') {
    const apiDir = path.join(__dirname, '..', 'app', 'api');
    const hiddenApiDir = path.join(__dirname, '..', 'app', '_api_hidden');

    if (fs.existsSync(apiDir)) {
      console.log('🚫 Temporarily hiding API directory for static build...');
      fs.renameSync(apiDir, hiddenApiDir);
    }
  }
}

// Restore API directory after build
function restoreApiDirectory() {
  if (target === 'static' || target === 'electron' || target === 'mobile') {
    const apiDir = path.join(__dirname, '..', 'app', 'api');
    const hiddenApiDir = path.join(__dirname, '..', 'app', '_api_hidden');

    if (fs.existsSync(hiddenApiDir)) {
      console.log('🔄 Restoring API directory...');
      fs.renameSync(hiddenApiDir, apiDir);
    }
  }
}

try {
  // Clean previous builds
  cleanPreviousBuilds();
  
  // Hide API directory for static builds
  hideApiDirectory();

  // Run the build
  console.log('⚡ Running Next.js build...');
  console.log(`📊 Build target: ${target}`);
  console.log(`🔧 NODE_ENV: ${env.NODE_ENV}`);
  console.log(`🎯 BUILD_TARGET: ${env.BUILD_TARGET}`);
  
  execSync('npx next build', { 
    stdio: 'inherit', 
    env,
    cwd: path.join(__dirname, '..') 
  });
  
  // Restore API directory
  restoreApiDirectory();
  
  // For static/electron/mobile builds, validate the export and fix RSC chunk paths
  if (target === 'static' || target === 'electron' || target === 'mobile') {
    validateStaticExport();

    // Fix RSC chunk paths for relative loading
    console.log('🔧 Fixing RSC chunk paths for static builds...');
    execSync('node scripts/fix-rsc-chunk-paths.js', {
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
  }
  
  // Post-build steps based on target
  switch (target) {
    case 'electron':
      console.log('🖥️  Copying static export to Electron app directory...');
      execSync('node scripts/copy-out-to-electron.js', {
        stdio: 'inherit',
        cwd: path.join(__dirname, '..')
      });

      // Validate Electron app directory
      const electronAppDir = path.join(__dirname, '..', 'electron', 'app');
      if (fs.existsSync(electronAppDir)) {
        const appFiles = fs.readdirSync(electronAppDir);
        console.log(`📁 Electron app directory contains ${appFiles.length} items`);

        if (!appFiles.includes('index.html')) {
          console.warn('⚠️  index.html not found in electron/app - app may not start properly');
        }
        if (!appFiles.some(f => f.startsWith('_next'))) {
          console.warn('⚠️  _next assets not found in electron/app - app may not function properly');
        }
      }
      break;
      
    case 'mobile':
      console.log('📱 Static export ready for Capacitor deployment...');
      console.log('💡 Next steps:');
      console.log('  1. Run: npx cap sync');
      console.log('  2. Run: npx cap build');
      break;
      
    case 'static':
      console.log('📦 Static export completed successfully!');
      console.log('💡 Files are ready in the out/ directory');
      break;
  }
  
  // Build summary
  console.log('\n🎉 Build Summary:');
  console.log(`✅ Target: ${target}`);
  console.log(`✅ Environment: ${env.NODE_ENV}`);
  
  if (target === 'static' || target === 'electron' || target === 'mobile') {
    const outDir = path.join(__dirname, '..', 'out');
    if (fs.existsSync(outDir)) {
      const stats = fs.statSync(outDir);
      console.log(`📁 Export directory: out/ (last modified: ${stats.mtime.toISOString()})`);
    }
  }
  
  console.log(`🚀 Build completed successfully for ${target}!`);
  
} catch (error) {
  // Always restore API directory even if build fails
  restoreApiDirectory();
  
  console.error(`\n❌ Build failed for ${target}:`);
  console.error(`💥 Error: ${error.message}`);
  
  // Helpful error suggestions
  if (error.message.includes('export')) {
    console.error('\n💡 Static export troubleshooting:');
    console.error('  1. Check for pages using server-side features (cookies, headers)');
    console.error('  2. Ensure all pages are either "use client" or have proper static exports');
    console.error('  3. Review next.config.ts for proper export configuration');
  }
  
  if (error.message.includes('electron')) {
    console.error('\n💡 Electron build troubleshooting:');
    console.error('  1. Ensure electron directory exists and is properly set up');
    console.error('  2. Check electron/package.json for correct configuration');
    console.error('  3. Verify copy-out-to-electron.js script is working');
  }
  
  process.exit(1);
}