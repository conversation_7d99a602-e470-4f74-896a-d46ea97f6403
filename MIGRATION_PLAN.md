# Next.js 15 + Electron + Capacitor Migration Plan
## From Over-Engineering to Native Simplicity

### 🎯 **Mission Statement**
Transform the current over-engineered Next.js 15 + Electron integration from 1000+ lines of custom asset coordination code to a **5-line native solution** that works universally for both Electron desktop and Capacitor mobile platforms.

---

## 📊 **Current State Analysis**

### **Problems Identified**
1. **Over-Engineered Asset Management**: 323+ lines in `asset-path-coordinator.ts`
2. **Complex Build System**: Multiple build targets with custom webpack modifications
3. **Manual Path Fixing**: Post-build HTML manipulation scripts
4. **Fighting Next.js**: Custom solutions instead of using native features
5. **Platform Fragmentation**: Different configurations for Electron vs Capacitor

### **Root Cause**
The core issue is **Next.js doesn't natively support relative paths** for static exports, but modern tooling has **standard solutions** that make custom implementations unnecessary.

---

## 🎯 **Target Architecture**

### **Universal Static Export Strategy**
- **One Build**: Single `next build` for both platforms
- **Relative Assets**: `assetPrefix: './'` for universal compatibility
- **Standard Tools**: `electron-serve` + Capacitor `webDir`
- **Minimal Config**: 5-line `next.config.ts`

### **Platform Compatibility Matrix**
| Platform | Protocol | Asset Loading | Configuration |
|----------|----------|---------------|---------------|
| Electron | `file://` | `electron-serve` | `directory: 'app'` |
| Capacitor | `file://` | Native webview | `webDir: 'out'` |
| Development | `http://` | Next.js dev server | `localhost:3000` |

---

## 📋 **Migration Phases**

### **Phase 1: Backup & Assessment** ⏱️ *30 minutes*

#### **1.1 Create Safety Backups**
```bash
# Create migration branch
git checkout -b migration/native-static-export
git add -A && git commit -m "Pre-migration backup"

# Backup critical files
mkdir -p migration-backup
cp -r lib/config migration-backup/
cp next.config.ts migration-backup/
cp -r scripts migration-backup/
cp package.json migration-backup/
cp electron/src/index.ts migration-backup/
```

#### **1.2 Document Current Complexity**
```bash
# Count lines of over-engineered code
echo "Current complexity analysis:"
wc -l lib/config/*.ts
wc -l scripts/fix-*.js
wc -l scripts/test-*.js
echo "Total custom asset management lines: $(cat lib/config/*.ts scripts/fix-*.js scripts/test-*.js | wc -l)"
```

#### **1.3 Test Current System**
```bash
# Ensure current system works before migration
npm run build:electron
npm run build:mobile
```

### **Phase 2: Implement Universal Configuration** ⏱️ *45 minutes*

#### **2.1 Create Minimal next.config.ts**
```typescript
// next.config.ts - Replace entire file with this
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  output: 'export',
  trailingSlash: true,
  images: { unoptimized: true },
  assetPrefix: './'
};

export default nextConfig;
```

#### **2.2 Create Universal Build Script**
```javascript
// scripts/build-universal.js
#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const target = process.argv[2] || 'all';
const startTime = Date.now();

console.log(`🚀 Universal Static Export Build`);
console.log(`📅 ${new Date().toISOString()}`);
console.log(`🎯 Target: ${target}`);
console.log(`📁 Working Directory: ${process.cwd()}`);

// Clean previous builds
function cleanBuilds() {
  console.log('\n🧹 Cleaning previous builds...');
  
  const dirsToClean = ['.next', 'out'];
  if (target === 'electron' || target === 'all') {
    dirsToClean.push('electron/app');
  }
  
  dirsToClean.forEach(dir => {
    if (fs.existsSync(dir)) {
      console.log(`  🗑️  Removing ${dir}/`);
      fs.rmSync(dir, { recursive: true, force: true });
    }
  });
  
  console.log('✅ Clean complete');
}

// Validate build output
function validateBuild() {
  console.log('\n🔍 Validating build output...');
  
  const outDir = 'out';
  if (!fs.existsSync(outDir)) {
    throw new Error(`❌ Build failed: ${outDir}/ directory not found`);
  }
  
  const requiredFiles = ['index.html', '_next'];
  requiredFiles.forEach(file => {
    const filePath = path.join(outDir, file);
    if (!fs.existsSync(filePath)) {
      throw new Error(`❌ Missing required file: ${file}`);
    }
  });
  
  // Check for relative paths in HTML
  const indexPath = path.join(outDir, 'index.html');
  const indexContent = fs.readFileSync(indexPath, 'utf8');
  
  if (indexContent.includes('href="/_next/') || indexContent.includes('src="/_next/')) {
    console.warn('⚠️  Found absolute paths in HTML - this may cause issues');
  } else {
    console.log('✅ All asset paths are relative');
  }
  
  console.log('✅ Build validation complete');
}

// Copy to platform directories
function copyToPlatforms() {
  if (target === 'electron' || target === 'all') {
    console.log('\n🖥️  Copying to Electron...');
    
    const electronAppDir = 'electron/app';
    if (fs.existsSync(electronAppDir)) {
      fs.rmSync(electronAppDir, { recursive: true });
    }
    
    fs.cpSync('out', electronAppDir, { recursive: true });
    
    const copiedFiles = fs.readdirSync(electronAppDir);
    console.log(`  📁 Copied ${copiedFiles.length} items to electron/app/`);
  }
  
  if (target === 'capacitor' || target === 'all') {
    console.log('\n📱 Capacitor build ready');
    console.log('  💡 Run: npm run cap:sync to update mobile apps');
  }
}

// Main build process
try {
  cleanBuilds();
  
  console.log('\n⚡ Running Next.js build...');
  console.log('📊 Configuration: Universal Static Export');
  
  execSync('npx next build', { 
    stdio: 'inherit',
    env: { 
      ...process.env,
      NODE_ENV: 'production'
    }
  });
  
  validateBuild();
  copyToPlatforms();
  
  const duration = ((Date.now() - startTime) / 1000).toFixed(1);
  
  console.log('\n🎉 Universal Build Complete!');
  console.log(`⏱️  Duration: ${duration}s`);
  console.log(`📦 Output: out/ directory`);
  
  if (target === 'electron' || target === 'all') {
    console.log(`🖥️  Electron: electron/app/ directory`);
  }
  
  console.log('\n📋 Next Steps:');
  if (target === 'electron' || target === 'all') {
    console.log('  🖥️  Electron: cd electron && npm run electron:start');
  }
  if (target === 'capacitor' || target === 'all') {
    console.log('  📱 Mobile: npm run cap:sync && npm run cap:android');
  }
  
} catch (error) {
  console.error(`\n❌ Build failed: ${error.message}`);
  console.error('\n🔧 Troubleshooting:');
  console.error('  1. Check Next.js configuration');
  console.error('  2. Verify all pages are static-export compatible');
  console.error('  3. Ensure no server-side features in static pages');
  process.exit(1);
}
```

#### **2.3 Update Package.json Scripts**
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "node scripts/build-universal.js all",
    "build:electron": "node scripts/build-universal.js electron",
    "build:mobile": "node scripts/build-universal.js capacitor",
    "build:web": "node scripts/build-universal.js web",
    
    "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && cd electron && npm run electron:start:dev\"",
    "electron:build": "npm run build:electron && cd electron && npm run build && npm run electron:build",
    "electron:start": "npm run build:electron && cd electron && npm run electron:start",
    
    "cap:sync": "npm run build:mobile && npx cap sync",
    "cap:android": "npm run cap:sync && npx cap run android",
    "cap:ios": "npm run cap:sync && npx cap run ios",
    "cap:build:android": "npm run cap:sync && npx cap build android",
    "cap:build:ios": "npm run cap:sync && npx cap build ios"
  }
}
```

### **Phase 3: Simplify Electron Implementation** ⏱️ *60 minutes*

#### **3.1 Install electron-serve**
```bash
cd electron
npm install electron-serve
```

#### **3.2 Create Minimal Electron Main Process**
```typescript
// electron/src/index.ts - Replace with minimal implementation
import { app, BrowserWindow, Menu, shell, ipcMain, dialog } from 'electron';
import * as path from 'path';
import * as serve from 'electron-serve';

// Environment detection
const isDev = process.env.NODE_ENV === 'development';
const isPackaged = app.isPackaged;

// Static file serving for production
const loadURL = serve({
  directory: 'app',
  scheme: 'app'
});

// Keep a global reference of the window object
let mainWindow: BrowserWindow | null = null;

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});

function createWindow(): void {
  // Create the browser window
  mainWindow = new BrowserWindow({
    title: 'Bistro',
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      // Security best practices
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      // Allow file:// protocol for static assets
      webSecurity: !isDev
    },
    show: false, // Don't show until ready
    icon: path.join(__dirname, '..', 'resources', 'icon.png')
  });

  // Load the app
  if (isDev) {
    // Development: Load from Next.js dev server
    const devUrl = 'http://localhost:3000';
    console.log(`🔧 Development mode: Loading ${devUrl}`);

    mainWindow.loadURL(devUrl).catch(err => {
      console.error('Failed to load development server:', err);
      dialog.showErrorBox(
        'Development Server Error',
        'Could not connect to Next.js development server.\n\nMake sure to run "npm run dev" first.'
      );
    });

    // Open DevTools in development
    mainWindow.webContents.openDevTools();
  } else {
    // Production: Load static files via electron-serve
    console.log('📦 Production mode: Loading static files');

    loadURL(mainWindow).catch(err => {
      console.error('Failed to load static files:', err);
      dialog.showErrorBox(
        'Application Error',
        'Could not load application files.\n\nPlease reinstall the application.'
      );
    });
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    if (mainWindow) {
      mainWindow.show();

      // Focus on app start
      if (isDev) {
        mainWindow.focus();
      }
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Setup application menu
  setupApplicationMenu();
}

function setupApplicationMenu(): void {
  const template: Electron.MenuItemConstructorOptions[] = [
    {
      label: 'File',
      submenu: [
        {
          label: 'Quit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(() => {
  createWindow();

  app.on('activate', () => {
    // On macOS, re-create window when dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Security: Prevent navigation to external URLs
app.on('web-contents-created', (event, contents) => {
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);

    if (parsedUrl.origin !== 'http://localhost:3000' && parsedUrl.protocol !== 'app:') {
      event.preventDefault();
      shell.openExternal(navigationUrl);
    }
  });
});

// Handle protocol for packaged app
if (!isDev) {
  app.setAsDefaultProtocolClient('bistro');
}

console.log(`🚀 Bistro Electron App Starting`);
console.log(`📊 Environment: ${isDev ? 'Development' : 'Production'}`);
console.log(`📦 Packaged: ${isPackaged}`);
console.log(`📁 App Path: ${app.getAppPath()}`);
```

#### **3.3 Update Electron Package.json Scripts**
```json
{
  "scripts": {
    "build": "tsc",
    "watch": "tsc -w",
    "electron:start": "npm run build && electron .",
    "electron:start:dev": "npm run build && cross-env NODE_ENV=development electron .",
    "electron:build": "npm run build && electron-builder build",
    "electron:dist": "npm run build && electron-builder"
  }
}
```

### **Phase 4: Optimize Capacitor Configuration** ⏱️ *30 minutes*

#### **4.1 Update Capacitor Config**
```json
{
  "appId": "dev.bistro.app",
  "appName": "Bistro",
  "webDir": "out",
  "server": {
    "androidScheme": "https",
    "iosScheme": "https"
  },
  "plugins": {
    "SplashScreen": {
      "launchShowDuration": 2000,
      "backgroundColor": "#ffffff",
      "showSpinner": false
    },
    "StatusBar": {
      "style": "default",
      "backgroundColor": "#ffffff"
    }
  }
}
```

#### **4.2 Verify Mobile Compatibility**
```bash
# Test static export compatibility
npm run build:mobile

# Check for mobile-specific issues
echo "Checking for mobile compatibility issues..."
grep -r "window\." out/ || echo "✅ No direct window references found"
grep -r "document\." out/ || echo "✅ No direct document references found"
```

### **Phase 5: Remove Over-Engineered Systems** ⏱️ *45 minutes*

#### **5.1 Delete Complex Asset Management**
```bash
# Remove over-engineered asset coordination
rm -rf lib/config/
rm -f scripts/fix-*.js
rm -f scripts/test-*.js
rm -f scripts/verify-*.js

# Remove manual path fixing scripts
rm -f scripts/fix-rsc-paths-manual.js
rm -f scripts/fix-rsc-chunk-paths.js
rm -f scripts/fix-electron-paths.js

# Remove complex build scripts
rm -f scripts/build.js  # Replace with build-universal.js
```

#### **5.2 Clean Package.json Dependencies**
```bash
# Remove unnecessary dependencies (review first)
npm uninstall webpack-asset-plugin
npm uninstall custom-asset-loaders
# Add any other over-engineered dependencies you identify
```

#### **5.3 Update .gitignore**
```gitignore
# Add to .gitignore
migration-backup/
*.backup
.next/
out/
electron/app/
electron/dist/
```

### **Phase 6: Testing & Validation** ⏱️ *60 minutes*

#### **6.1 Test Universal Build**
```bash
# Test universal build
npm run build

# Verify output structure
echo "Build output structure:"
tree out/ -L 2
tree electron/app/ -L 2
```

#### **6.2 Test Electron Desktop**
```bash
# Test Electron in development
npm run electron:dev

# Test Electron production build
npm run electron:start

# Test Electron packaging
npm run electron:build
```

#### **6.3 Test Capacitor Mobile**
```bash
# Test Capacitor sync
npm run cap:sync

# Test Android (if available)
npm run cap:android

# Test iOS (if available)
npm run cap:ios
```

#### **6.4 Validation Checklist**
- [ ] Static export builds without errors
- [ ] All assets use relative paths (`./_next/`)
- [ ] Electron loads without blank screen
- [ ] Capacitor mobile apps launch properly
- [ ] No console errors in any platform
- [ ] Navigation works in all platforms
- [ ] Images and fonts load correctly
- [ ] No 404 errors for assets

### **Phase 7: Documentation & Cleanup** ⏱️ *30 minutes*

#### **7.1 Create New README Section**
```markdown
## Build System

### Universal Static Export
This project uses a universal static export system that works for both Electron desktop and Capacitor mobile platforms.

#### Quick Start
```bash
# Development
npm run dev                    # Next.js dev server
npm run electron:dev          # Electron + Next.js dev

# Production Builds
npm run build                 # Universal build (all platforms)
npm run build:electron        # Electron-specific build
npm run build:mobile          # Mobile-specific build

# Platform Deployment
npm run electron:build        # Package Electron app
npm run cap:android           # Run on Android
npm run cap:ios              # Run on iOS
```

#### Architecture
- **Next.js 15**: Static export with relative assets
- **Electron**: Uses `electron-serve` for file serving
- **Capacitor**: Uses `webDir: 'out'` for mobile deployment
- **Universal**: Single build works for all platforms
```

#### **7.2 Performance Comparison**
```bash
# Document the improvement
echo "Migration Results:" > MIGRATION_RESULTS.md
echo "==================" >> MIGRATION_RESULTS.md
echo "" >> MIGRATION_RESULTS.md
echo "Before Migration:" >> MIGRATION_RESULTS.md
echo "- next.config.ts: $(wc -l < migration-backup/next.config.ts) lines" >> MIGRATION_RESULTS.md
echo "- Asset coordination: $(cat migration-backup/lib/config/*.ts | wc -l) lines" >> MIGRATION_RESULTS.md
echo "- Build scripts: $(cat migration-backup/scripts/fix-*.js migration-backup/scripts/test-*.js | wc -l) lines" >> MIGRATION_RESULTS.md
echo "" >> MIGRATION_RESULTS.md
echo "After Migration:" >> MIGRATION_RESULTS.md
echo "- next.config.ts: $(wc -l < next.config.ts) lines" >> MIGRATION_RESULTS.md
echo "- Build script: $(wc -l < scripts/build-universal.js) lines" >> MIGRATION_RESULTS.md
echo "- Electron main: $(wc -l < electron/src/index.ts) lines" >> MIGRATION_RESULTS.md
echo "" >> MIGRATION_RESULTS.md
echo "Reduction: $(echo "scale=1; ($(cat migration-backup/lib/config/*.ts migration-backup/scripts/fix-*.js migration-backup/scripts/test-*.js | wc -l) - $(wc -l < scripts/build-universal.js)) / $(cat migration-backup/lib/config/*.ts migration-backup/scripts/fix-*.js migration-backup/scripts/test-*.js | wc -l) * 100" | bc)% less code" >> MIGRATION_RESULTS.md
```

---

## 🎯 **Success Metrics**

### **Code Reduction**
- [ ] **90%+ reduction** in asset management code
- [ ] **next.config.ts**: From 123 lines to 5 lines
- [ ] **Build system**: From multiple scripts to single universal script
- [ ] **Electron main**: From complex loading to 20-line implementation

### **Functionality Preservation**
- [ ] All existing features work in Electron
- [ ] All existing features work in Capacitor mobile
- [ ] Development workflow unchanged
- [ ] Build times improved
- [ ] No regression in user experience

### **Maintainability Improvement**
- [ ] Standard tooling throughout
- [ ] No custom webpack modifications
- [ ] No post-build manipulation
- [ ] Clear, documented architecture
- [ ] Easy to upgrade Next.js versions

---

## 🚨 **Risk Mitigation**

### **Rollback Plan**
```bash
# If migration fails, rollback is simple:
git checkout main
git branch -D migration/native-static-export

# Or restore from backup:
cp -r migration-backup/* .
npm install
```

### **Testing Strategy**
1. **Incremental Testing**: Test each phase before proceeding
2. **Platform Isolation**: Test Electron and Capacitor separately
3. **Feature Validation**: Ensure all existing features work
4. **Performance Monitoring**: Compare build times and app performance

### **Common Issues & Solutions**

#### **Issue: Blank Screen in Electron**
```bash
# Debug steps:
1. Check electron/app/ directory exists and has files
2. Verify index.html has relative paths (./next/ not /_next/)
3. Open DevTools and check console for errors
4. Ensure electron-serve is properly installed
```

#### **Issue: Capacitor Build Fails**
```bash
# Debug steps:
1. Verify out/ directory exists after build
2. Check capacitor.config.json webDir points to "out"
3. Run npx cap doctor for configuration issues
4. Ensure all assets use relative paths
```

#### **Issue: Assets Not Loading**
```bash
# Debug steps:
1. Verify assetPrefix: './' in next.config.ts
2. Check HTML source for relative paths
3. Ensure trailingSlash: true is set
4. Test with simple static file first
```

---

## 📈 **Expected Outcomes**

### **Immediate Benefits**
- **Simplified Development**: Single build command for all platforms
- **Reduced Complexity**: 90% less custom code to maintain
- **Faster Builds**: No post-processing or path manipulation
- **Better Reliability**: Uses standard, well-tested tools

### **Long-term Benefits**
- **Easier Upgrades**: Standard Next.js configuration
- **Better Documentation**: Clear, simple architecture
- **Reduced Bugs**: Less custom code means fewer edge cases
- **Team Productivity**: Easier onboarding and maintenance

### **Technical Debt Elimination**
- **No More Custom Webpack**: Uses Next.js as intended
- **No More Path Fixing**: Relative paths work natively
- **No More Build Target Complexity**: Universal build approach
- **No More Asset Coordination**: Standard tooling handles everything

---

## 🎉 **Migration Complete Checklist**

- [ ] **Phase 1**: Backups created and current system documented
- [ ] **Phase 2**: Universal configuration implemented and tested
- [ ] **Phase 3**: Electron simplified and working
- [ ] **Phase 4**: Capacitor optimized and tested
- [ ] **Phase 5**: Over-engineered systems removed
- [ ] **Phase 6**: All platforms tested and validated
- [ ] **Phase 7**: Documentation updated and results documented

### **Final Validation**
- [ ] `npm run build` works without errors
- [ ] `npm run electron:start` launches app successfully
- [ ] `npm run cap:android` works on mobile
- [ ] All existing features functional
- [ ] No console errors in any platform
- [ ] Build time improved
- [ ] Code complexity reduced by 90%+

**🎊 Congratulations! You've successfully migrated from over-engineering to native simplicity!**

---

## 📚 **Additional Resources**

### **Official Documentation**
- [Next.js Static Exports](https://nextjs.org/docs/app/guides/static-exports)
- [Electron Serve Documentation](https://github.com/sindresorhus/electron-serve)
- [Capacitor Configuration](https://capacitorjs.com/docs/config)

### **Best Practices**
- [Next.js + Electron Integration Patterns](https://github.com/vercel/next.js/discussions/32216)
- [Universal Static Export Strategies](https://medium.com/@kirill.konshin/the-ultimate-electron-app-with-next-js-and-react-server-components-a5c0cabda72b)
- [Mobile-First Static Export Design](https://capacitorjs.com/docs/guides/deploying-updates)

### **Troubleshooting**
- [Common Next.js Static Export Issues](https://nextjs.org/docs/app/guides/static-exports#unsupported-features)
- [Electron File Protocol Best Practices](https://www.electronjs.org/docs/latest/api/protocol)
- [Capacitor WebView Debugging](https://capacitorjs.com/docs/guides/debugging)

---

## 🔄 **Continuous Improvement**

### **Post-Migration Optimizations**
1. **Performance Monitoring**: Track build times and app startup performance
2. **Bundle Analysis**: Use Next.js bundle analyzer to optimize asset sizes
3. **Security Audit**: Regular security reviews of simplified codebase
4. **Dependency Updates**: Easier to maintain with standard tooling

### **Future Enhancements**
1. **Progressive Web App**: Add PWA features using standard Next.js patterns
2. **Auto-Updates**: Implement Electron auto-updater with simplified build
3. **Code Splitting**: Optimize bundle sizes with Next.js dynamic imports
4. **Testing Automation**: Add automated testing for all platforms

### **Monitoring & Maintenance**
1. **Build Health**: Monitor build success rates across platforms
2. **Error Tracking**: Implement error monitoring for production apps
3. **Performance Metrics**: Track app performance across platforms
4. **User Feedback**: Collect feedback on improved development experience

**This migration plan transforms your complex, over-engineered system into a simple, maintainable, and future-proof architecture that leverages the best of modern Next.js, Electron, and Capacitor ecosystems.**
```
