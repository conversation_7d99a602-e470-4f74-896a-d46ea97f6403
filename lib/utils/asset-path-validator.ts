/**
 * Asset Path Validation Utilities
 * 
 * Provides comprehensive validation for asset paths during build time
 * to ensure consistency and prevent loading issues in static exports.
 */

import { AssetType, AssetPathError, ValidationResult, ValidationError, ValidationWarning } from '../config/asset-path-coordinator';

export interface PathValidationOptions {
  buildTarget: string;
  staticExport: boolean;
  strictMode: boolean;
  allowedExtensions: string[];
}

export interface FileAssetReference {
  file: string;
  line: number;
  column: number;
  path: string;
  type: AssetType;
  context: string;
}

/**
 * Asset Path Validator - Validates asset paths for consistency
 */
export class AssetPathValidator {
  private options: PathValidationOptions;

  constructor(options: Partial<PathValidationOptions> = {}) {
    this.options = {
      buildTarget: process.env.BUILD_TARGET || 'web',
      staticExport: process.env.BUILD_TARGET === 'electron' || process.env.BUILD_TARGET === 'mobile',
      strictMode: true,
      allowedExtensions: ['.js', '.css', '.woff', '.woff2', '.ttf', '.otf', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'],
      ...options
    };
  }

  /**
   * Validate a single asset path
   */
  validateAssetPath(path: string, assetType: AssetType, file?: string): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Check for absolute paths in static exports
    if (this.options.staticExport && this.isAbsolutePath(path)) {
      errors.push({
        type: AssetPathError.INCONSISTENT_PATHS,
        file: file || 'unknown',
        originalPath: path,
        expectedPath: this.convertToRelativePath(path, assetType),
        suggestion: 'Convert absolute path to relative for static export compatibility'
      });
    }

    // Check for missing _next prefix in chunks
    if (this.options.staticExport && assetType === AssetType.CHUNK) {
      if (!path.startsWith('./_next/') && path.includes('static/chunks/')) {
        errors.push({
          type: AssetPathError.BROKEN_CHUNK_REFERENCES,
          file: file || 'unknown',
          originalPath: path,
          expectedPath: './_next/' + path.replace(/^\//, ''),
          suggestion: 'Add ./_next/ prefix for chunk references in static exports'
        });
      }
    }

    // Check font asset paths
    if (assetType === AssetType.FONT) {
      if (this.options.staticExport && !this.isValidFontPath(path)) {
        errors.push({
          type: AssetPathError.MISSING_FONT_ASSETS,
          file: file || 'unknown',
          originalPath: path,
          expectedPath: this.convertToRelativePath(path, AssetType.FONT),
          suggestion: 'Ensure font paths are relative and accessible in static export'
        });
      }
    }

    // Check for invalid public path references
    if (this.options.staticExport && path.includes('/_next/') && !path.startsWith('./_next/')) {
      errors.push({
        type: AssetPathError.INVALID_PUBLIC_PATH,
        file: file || 'unknown',
        originalPath: path,
        expectedPath: path.replace('/_next/', './_next/'),
        suggestion: 'Use relative public path for static export compatibility'
      });
    }

    // Warnings for potential issues
    if (path.includes('http://') || path.includes('https://')) {
      warnings.push({
        type: 'external_reference',
        message: 'External asset reference may not work in offline environments',
        file: file || 'unknown',
        path
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate multiple asset references
   */
  validateAssetReferences(references: FileAssetReference[]): ValidationResult {
    const allErrors: ValidationError[] = [];
    const allWarnings: ValidationWarning[] = [];

    for (const ref of references) {
      const result = this.validateAssetPath(ref.path, ref.type, ref.file);
      allErrors.push(...result.errors);
      allWarnings.push(...result.warnings);
    }

    // Additional cross-reference validation
    const crossValidation = this.validateCrossReferences(references);
    allErrors.push(...crossValidation.errors);
    allWarnings.push(...crossValidation.warnings);

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings
    };
  }

  /**
   * Validate consistency across multiple references
   */
  private validateCrossReferences(references: FileAssetReference[]): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Group references by type
    const byType = references.reduce((acc, ref) => {
      if (!acc[ref.type]) acc[ref.type] = [];
      acc[ref.type].push(ref);
      return acc;
    }, {} as Record<AssetType, FileAssetReference[]>);

    // Check for inconsistent path patterns within each type
    Object.entries(byType).forEach(([type, refs]) => {
      const paths = refs.map(r => r.path);
      const absolutePaths = paths.filter(p => this.isAbsolutePath(p));
      const relativePaths = paths.filter(p => !this.isAbsolutePath(p));

      if (this.options.staticExport && absolutePaths.length > 0 && relativePaths.length > 0) {
        errors.push({
          type: AssetPathError.INCONSISTENT_PATHS,
          file: 'multiple',
          originalPath: `Mixed path types in ${type} assets`,
          expectedPath: 'All relative paths',
          suggestion: `Convert all ${type} asset paths to relative for consistency`
        });
      }
    });

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Check if a path is absolute
   */
  private isAbsolutePath(path: string): boolean {
    return path.startsWith('/') || path.includes('://');
  }

  /**
   * Check if a font path is valid for static exports
   */
  private isValidFontPath(path: string): boolean {
    if (!this.options.staticExport) return true;
    
    // Font paths should be relative and not external
    return !this.isAbsolutePath(path) && !path.includes('://');
  }

  /**
   * Convert absolute path to relative
   */
  private convertToRelativePath(path: string, assetType: AssetType): string {
    // Remove leading slash
    let relativePath = path.startsWith('/') ? path.substring(1) : path;
    
    // Add appropriate prefix based on asset type
    switch (assetType) {
      case AssetType.CHUNK:
      case AssetType.CSS:
        if (!relativePath.startsWith('_next/')) {
          relativePath = '_next/' + relativePath;
        }
        return './' + relativePath;
      
      case AssetType.FONT:
      case AssetType.STATIC:
      case AssetType.IMAGE:
        return './' + relativePath;
      
      default:
        return './' + relativePath;
    }
  }

  /**
   * Extract asset references from HTML content
   */
  extractAssetReferences(htmlContent: string, filename: string): FileAssetReference[] {
    const references: FileAssetReference[] = [];
    const lines = htmlContent.split('\n');

    lines.forEach((line, lineIndex) => {
      // Extract script src references
      const scriptRegex = /<script[^>]+src=["']([^"']+)["']/g;
      let scriptMatch;
      while ((scriptMatch = scriptRegex.exec(line)) !== null) {
        references.push({
          file: filename,
          line: lineIndex + 1,
          column: scriptMatch.index || 0,
          path: scriptMatch[1],
          type: AssetType.CHUNK,
          context: line.trim()
        });
      }

      // Extract link href references (CSS and fonts)
      const linkRegex = /<link[^>]+href=["']([^"']+)["']/g;
      let linkMatch;
      while ((linkMatch = linkRegex.exec(line)) !== null) {
        const href = linkMatch[1];
        const type = href.includes('.css') ? AssetType.CSS : 
                    href.includes('.woff') || href.includes('.ttf') ? AssetType.FONT : 
                    AssetType.STATIC;
        
        references.push({
          file: filename,
          line: lineIndex + 1,
          column: linkMatch.index || 0,
          path: href,
          type,
          context: line.trim()
        });
      }

      // Extract img src references
      const imgRegex = /<img[^>]+src=["']([^"']+)["']/g;
      let imgMatch;
      while ((imgMatch = imgRegex.exec(line)) !== null) {
        references.push({
          file: filename,
          line: lineIndex + 1,
          column: imgMatch.index || 0,
          path: imgMatch[1],
          type: AssetType.IMAGE,
          context: line.trim()
        });
      }
    });

    return references;
  }

  /**
   * Generate validation report
   */
  generateValidationReport(result: ValidationResult): string {
    const lines: string[] = [];
    
    lines.push('Asset Path Validation Report');
    lines.push('================================');
    lines.push('');
    
    if (result.isValid) {
      lines.push('✅ All asset paths are valid');
    } else {
      lines.push(`❌ Found ${result.errors.length} error(s)`);
    }
    
    if (result.warnings.length > 0) {
      lines.push(`⚠️  Found ${result.warnings.length} warning(s)`);
    }
    
    lines.push('');

    // Report errors
    if (result.errors.length > 0) {
      lines.push('Errors:');
      lines.push('-------');
      result.errors.forEach((error, index) => {
        lines.push(`${index + 1}. ${error.type} in ${error.file}`);
        lines.push(`   Original: ${error.originalPath}`);
        lines.push(`   Expected: ${error.expectedPath}`);
        lines.push(`   Suggestion: ${error.suggestion}`);
        lines.push('');
      });
    }

    // Report warnings
    if (result.warnings.length > 0) {
      lines.push('Warnings:');
      lines.push('---------');
      result.warnings.forEach((warning, index) => {
        lines.push(`${index + 1}. ${warning.type} in ${warning.file}`);
        lines.push(`   Path: ${warning.path}`);
        lines.push(`   Message: ${warning.message}`);
        lines.push('');
      });
    }

    return lines.join('\n');
  }
}

// Create default validator instance
export const assetPathValidator = new AssetPathValidator();

// Helper functions
export const validateAssetPath = (path: string, assetType: AssetType, file?: string) =>
  assetPathValidator.validateAssetPath(path, assetType, file);

export const validateAssetReferences = (references: FileAssetReference[]) =>
  assetPathValidator.validateAssetReferences(references);

export const extractAssetReferences = (htmlContent: string, filename: string) =>
  assetPathValidator.extractAssetReferences(htmlContent, filename);

export const generateValidationReport = (result: ValidationResult) =>
  assetPathValidator.generateValidationReport(result);