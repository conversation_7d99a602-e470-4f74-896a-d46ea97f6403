/**
 * Webpack Asset Plugin
 * 
 * Custom webpack plugin to ensure consistent asset path handling
 * for static exports, including dynamic imports and chunk loading.
 */

// Use any types for webpack to avoid dependency issues
type Compiler = any;
type WebpackPluginInstance = any;
import { AssetPathCoordinator, BuildTarget, AssetType } from './asset-path-coordinator';
import { runtimeChunkManager, injectRuntimeCode } from './runtime-chunk-manager';
import { applyRSCFixes } from './rsc-payload-fixer';

export interface WebpackAssetPluginOptions {
  buildTarget?: BuildTarget;
  enableValidation?: boolean;
  enableRuntimeFix?: boolean;
}

export class WebpackAssetPlugin implements WebpackPluginInstance {
  private coordinator: AssetPathCoordinator;
  private options: WebpackAssetPluginOptions;

  constructor(options: WebpackAssetPluginOptions = {}) {
    this.options = {
      enableValidation: true,
      enableRuntimeFix: true,
      ...options
    };
    this.coordinator = new AssetPathCoordinator(options.buildTarget);
  }

  apply(compiler: Compiler): void {
    const pluginName = 'WebpackAssetPlugin';
    
    // Only apply for static exports
    if (!this.coordinator.isStaticExport()) {
      return;
    }

    // Configure chunk loading for static exports
    compiler.hooks.compilation.tap(pluginName, (compilation) => {
      // Ensure consistent public path for all assets
      compilation.hooks.processAssets.tap(
        {
          name: pluginName,
          stage: compiler.webpack.Compilation.PROCESS_ASSETS_STAGE_OPTIMIZE
        },
        (assets) => {
          this.processAssets(assets, compilation);
        }
      );
    });

    // Configure runtime chunk loading with enhanced fixes
    compiler.hooks.thisCompilation.tap(pluginName, (compilation) => {
      compilation.hooks.runtimeRequirementInTree.for(compiler.webpack.RuntimeGlobals.publicPath)
        .tap(pluginName, (chunk, set) => {
          if (this.coordinator.isStaticExport()) {
            // Inject enhanced runtime public path management
            compilation.addRuntimeModule(chunk, new EnhancedStaticExportRuntimeModule());
          }
        });
      
      // Also handle chunk loading requirements
      compilation.hooks.runtimeRequirementInTree.for(compiler.webpack.RuntimeGlobals.ensureChunk)
        .tap(pluginName, (chunk, set) => {
          if (this.coordinator.isStaticExport()) {
            // Ensure chunk loading works with relative paths
            compilation.addRuntimeModule(chunk, new ChunkLoadingRuntimeModule());
          }
        });
    });
  }

  private processAssets(assets: any, compilation: any): void {
    const publicPath = this.coordinator.getPublicPath();
    
    // Process HTML files to fix asset references and inject runtime code
    Object.keys(assets).forEach(filename => {
      if (filename.endsWith('.html')) {
        const asset = assets[filename];
        let content = asset.source();
        
        // Fix script src paths
        content = content.replace(
          /<script[^>]+src=["']([^"']+)["']/g,
          (match: string, src: string) => {
            const resolvedPath = this.coordinator.resolveAssetPath(AssetType.CHUNK, src);
            return match.replace(src, resolvedPath);
          }
        );
        
        // Fix link href paths (CSS and fonts)
        content = content.replace(
          /<link[^>]+href=["']([^"']+)["']/g,
          (match: string, href: string) => {
            const assetType = href.includes('.css') ? AssetType.CSS : AssetType.FONT;
            const resolvedPath = this.coordinator.resolveAssetPath(assetType, href);
            return match.replace(href, resolvedPath);
          }
        );
        
        // Inject critical runtime code for chunk loading fixes
        content = injectRuntimeCode(content);
        
        // Apply RSC payload fixes
        content = applyRSCFixes(content);
        
        // Update the asset
        const webpack = require('webpack');
        compilation.updateAsset(filename, new webpack.sources.RawSource(content));
      }
    });
  }
}

/**
 * Enhanced runtime module for static export public path management
 */
class EnhancedStaticExportRuntimeModule extends (require('webpack').RuntimeModule) {
  constructor() {
    super('enhanced-static-export-runtime', 5); // High priority
  }

  generate(): string {
    return runtimeChunkManager.generateRuntimeCode();
  }
}

/**
 * Runtime module specifically for chunk loading fixes
 */
class ChunkLoadingRuntimeModule extends (require('webpack').RuntimeModule) {
  constructor() {
    super('chunk-loading-runtime', 6);
  }

  generate(): string {
    return `
// Chunk loading runtime fixes for static exports
(function() {
  if (typeof window === 'undefined') return;
  
  // Ensure all chunk loading uses relative paths
  if (typeof __webpack_require__.f !== 'undefined' && __webpack_require__.f.j) {
    const originalJsonpChunkLoading = __webpack_require__.f.j;
    __webpack_require__.f.j = function(chunkId, promises) {
      // Apply path corrections before loading
      return originalJsonpChunkLoading.call(this, chunkId, promises);
    };
  }
  
  // Fix dynamic import resolution
  if (typeof __webpack_require__.oe === 'function') {
    const originalOnError = __webpack_require__.oe;
    __webpack_require__.oe = function(err) {
      console.error('[Static Export] Chunk loading error:', err);
      return originalOnError.call(this, err);
    };
  }
})();
`;
  }
}

export default WebpackAssetPlugin;