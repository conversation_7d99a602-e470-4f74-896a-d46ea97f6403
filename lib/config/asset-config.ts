/**
 * Centralized Asset Configuration
 * 
 * Provides unified configuration for all asset types across different build targets.
 * This module serves as the single source of truth for asset handling configuration.
 */

import { AssetPathCoordinator, BuildTarget, AssetType } from './asset-path-coordinator';

export interface StaticExportConfig {
  assetPrefix: string;
  basePath: string;
  output: 'export';
  trailingSlash: boolean;
  images: { unoptimized: boolean };
  experimental: {
    optimizeCss: boolean;
    esmExternals: boolean;
  };
}

export interface WebpackAssetConfig {
  output: {
    publicPath: string;
    assetModuleFilename: string;
  };
  resolve: {
    alias: Record<string, string>;
  };
}

export interface FontAssetConfig {
  fonts: GoogleFontConfig[];
  staticExport: boolean;
  assetPrefix: string;
  cssVariables: boolean;
  optimization: boolean;
}

export interface GoogleFontConfig {
  family: string;
  subsets: string[];
  weights: string[];
  display: 'swap' | 'block' | 'fallback';
  variable: string;
}

export interface ImageAssetConfig {
  unoptimized: boolean;
  domains: string[];
  formats: string[];
  deviceSizes: number[];
  imageSizes: number[];
}

export interface ChunkAssetConfig {
  publicPath: string;
  chunkFilename: string;
  assetModuleFilename: string;
}

export interface CSSAssetConfig {
  publicPath: string;
  filename: string;
  chunkFilename: string;
}

/**
 * Unified Asset Configuration Manager
 */
export class AssetConfigManager {
  private coordinator: AssetPathCoordinator;

  constructor(buildTarget?: BuildTarget) {
    this.coordinator = new AssetPathCoordinator(buildTarget);
  }

  /**
   * Get Next.js static export configuration
   */
  getStaticExportConfig(): StaticExportConfig | null {
    if (!this.coordinator.isStaticExport()) {
      return null;
    }

    return {
      assetPrefix: this.coordinator.getAssetPrefix(),
      basePath: '',
      output: 'export',
      trailingSlash: true,
      images: { unoptimized: true },
      experimental: {
        optimizeCss: false,
        esmExternals: true
      }
    };
  }

  /**
   * Get webpack asset configuration
   */
  getWebpackAssetConfig(): WebpackAssetConfig {
    const config = this.coordinator.getWebpackConfig();
    
    return {
      output: {
        publicPath: config.output.publicPath,
        assetModuleFilename: config.output.assetModuleFilename
      },
      resolve: {
        alias: {
          // Add any necessary aliases here
          ...config.resolve.alias
        }
      }
    };
  }

  /**
   * Get font asset configuration
   */
  getFontAssetConfig(): FontAssetConfig {
    const fontConfig = this.coordinator.getFontConfig();
    
    return {
      fonts: [
        {
          family: 'Inter',
          subsets: ['latin'],
          weights: ['400', '500', '600', '700'],
          display: 'swap',
          variable: '--font-inter'
        },
        {
          family: 'Almarai',
          subsets: ['arabic'],
          weights: ['400', '700'],
          display: 'swap',
          variable: '--font-almarai'
        },
        {
          family: 'Tajawal',
          subsets: ['arabic'],
          weights: ['400', '500', '700'],
          display: 'swap',
          variable: '--font-tajawal'
        },
        {
          family: 'Changa',
          subsets: ['arabic'],
          weights: ['400', '500', '600', '700'],
          display: 'swap',
          variable: '--font-changa'
        }
      ],
      staticExport: fontConfig.staticExport,
      assetPrefix: fontConfig.assetPrefix,
      cssVariables: fontConfig.cssVariables,
      optimization: fontConfig.optimization
    };
  }

  /**
   * Get image asset configuration
   */
  getImageAssetConfig(): ImageAssetConfig {
    const isStatic = this.coordinator.isStaticExport();
    
    return {
      unoptimized: isStatic,
      domains: isStatic ? [] : ['res.cloudinary.com'],
      formats: [], // Remove formats to avoid type issues
      deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
      imageSizes: [16, 32, 48, 64, 96, 128, 256, 384]
    };
  }

  /**
   * Get JavaScript chunk configuration
   */
  getChunkAssetConfig(): ChunkAssetConfig {
    const publicPath = this.coordinator.getPublicPath();
    
    return {
      publicPath,
      chunkFilename: 'static/chunks/[name].[contenthash].js',
      assetModuleFilename: 'static/media/[name].[hash][ext]'
    };
  }

  /**
   * Get CSS asset configuration
   */
  getCSSAssetConfig(): CSSAssetConfig {
    const publicPath = this.coordinator.getPublicPath();
    
    return {
      publicPath,
      filename: 'static/css/[name].[contenthash].css',
      chunkFilename: 'static/css/[name].[contenthash].css'
    };
  }

  /**
   * Get complete asset configuration for all types
   */
  getAllAssetConfigs() {
    return {
      staticExport: this.getStaticExportConfig(),
      webpack: this.getWebpackAssetConfig(),
      fonts: this.getFontAssetConfig(),
      images: this.getImageAssetConfig(),
      chunks: this.getChunkAssetConfig(),
      css: this.getCSSAssetConfig(),
      buildTarget: this.coordinator.getConfig().buildTarget,
      isStaticExport: this.coordinator.isStaticExport()
    };
  }

  /**
   * Validate all asset configurations
   */
  validateConfiguration() {
    const config = this.coordinator.getConfig();
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate static export configuration
    if (config.staticExport) {
      if (!config.assetPrefix.startsWith('./')) {
        errors.push('Asset prefix must start with "./" for static exports');
      }
      
      if (!config.publicPath.startsWith('./_next/')) {
        errors.push('Public path must start with "./_next/" for static exports');
      }
    }

    // Validate font configuration
    const fontConfig = this.getFontAssetConfig();
    if (fontConfig.fonts.length === 0) {
      warnings.push('No fonts configured');
    }

    // Validate image configuration
    const imageConfig = this.getImageAssetConfig();
    if (config.staticExport && !imageConfig.unoptimized) {
      errors.push('Images must be unoptimized for static exports');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}

// Create singleton instance
export const assetConfigManager = new AssetConfigManager();

// Helper functions for easy access
export const getAssetConfigs = () => assetConfigManager.getAllAssetConfigs();
export const validateAssetConfig = () => assetConfigManager.validateConfiguration();
export const getStaticExportConfig = () => assetConfigManager.getStaticExportConfig();
export const getWebpackAssetConfig = () => assetConfigManager.getWebpackAssetConfig();
export const getFontAssetConfig = () => assetConfigManager.getFontAssetConfig();
export const getImageAssetConfig = () => assetConfigManager.getImageAssetConfig();