/**
 * Asset Path Configuration System
 * 
 * Unified export for all asset path configuration utilities.
 * This provides a single entry point for the asset path coordination system.
 */

// Core coordinator and types
export {
  AssetPathCoordinator,
  BuildTarget,
  AssetType,
  AssetPathConfig,
  AssetReference,
  ValidationResult,
  ValidationError,
  ValidationWarning,
  AssetPathError,
  assetPathCoordinator,
  getAssetPrefix,
  getPublicPath,
  resolveAssetPath,
  validateAssetPaths,
  isStaticExport
} from './asset-path-coordinator';

// Asset configuration manager
export {
  AssetConfigManager,
  StaticExportConfig,
  WebpackAssetConfig,
  FontAssetConfig,
  GoogleFontConfig,
  ImageAssetConfig,
  ChunkAssetConfig,
  CSSAssetConfig,
  assetConfigManager,
  getAssetConfigs,
  validateAssetConfig,
  getStaticExportConfig,
  getWebpackAssetConfig,
  getFontAssetConfig,
  getImageAssetConfig
} from './asset-config';

// Path validation utilities
export {
  AssetPathValidator,
  PathValidationOptions,
  FileAssetReference,
  assetPathValidator,
  validateAssetPath,
  validateAssetReferences,
  extractAssetReferences,
  generateValidationReport
} from '../utils/asset-path-validator';

// Build target management
export {
  BuildTargetManager,
  BuildTargetConfig,
  buildTargetManager,
  getCurrentBuildTarget,
  isStaticExportBuild,
  requiresRelativePathsBuild,
  getNextConfigOverrides,
  getWebpackConfigOverrides,
  validateBuildConfiguration
} from './build-target-manager';

// Webpack plugin and runtime fixes
export { WebpackAssetPlugin, WebpackAssetPluginOptions } from './webpack-asset-plugin';
export { RuntimeChunkManager, runtimeChunkManager, injectRuntimeCode } from './runtime-chunk-manager';
export { RSCPayloadFixer, rscPayloadFixer, applyRSCFixes } from './rsc-payload-fixer';

// Re-export app version for completeness
export { APP_VERSION } from './app-version';