/**
 * Runtime Chunk Manager
 * 
 * Handles dynamic import loading and chunk reference validation for static exports.
 * This is critical for fixing the blank screen issue in Electron and mobile builds.
 */

export interface ChunkLoadingConfig {
  publicPath: string;
  enablePathCorrection: boolean;
  enableRSCFix: boolean;
  enableChunkValidation: boolean;
  debugMode: boolean;
}

export class RuntimeChunkManager {
  private config: ChunkLoadingConfig;

  constructor(config: Partial<ChunkLoadingConfig> = {}) {
    this.config = {
      publicPath: './_next/',
      enablePathCorrection: true,
      enableRSCFix: true,
      enableChunkValidation: true,
      debugMode: false,
      ...config
    };
  }

  /**
   * Generate runtime code for chunk loading fixes
   */
  generateRuntimeCode(): string {
    return `
// Runtime Chunk Manager - Critical fixes for static export loading
(function() {
  'use strict';
  
  const config = ${JSON.stringify(this.config)};
  const isStaticExport = typeof window !== 'undefined';
  
  if (!isStaticExport) return; // Only apply fixes in browser environment
  
  // Debug logging
  function debugLog(message, ...args) {
    if (config.debugMode) {
      console.log('[RuntimeChunkManager]', message, ...args);
    }
  }
  
  debugLog('Initializing runtime chunk manager for static export');
  
  // 1. Fix webpack public path
  if (typeof __webpack_require__ !== 'undefined') {
    __webpack_require__.p = config.publicPath;
    debugLog('Set webpack public path to:', config.publicPath);
  }
  
  // 2. Fix dynamic import loading
  if (config.enablePathCorrection && typeof __webpack_require__.l === 'function') {
    const originalLoad = __webpack_require__.l;
    __webpack_require__.l = function(url, done, key, chunkId) {
      const originalUrl = url;
      
      // Convert absolute paths to relative
      if (!url.startsWith('./') && !url.startsWith('http')) {
        if (url.startsWith('/_next/')) {
          url = '.' + url;
        } else if (url.startsWith('/')) {
          url = '.' + url;
        }
      }
      
      if (originalUrl !== url) {
        debugLog('Corrected chunk URL:', originalUrl, '->', url);
      }
      
      return originalLoad.call(this, url, done, key, chunkId);
    };
  }
  
  // 3. Fix chunk loading with retry mechanism
  if (config.enableChunkValidation && typeof __webpack_require__.e === 'function') {
    const originalEnsure = __webpack_require__.e;
    __webpack_require__.e = function(chunkId) {
      debugLog('Loading chunk:', chunkId);
      
      return originalEnsure.call(this, chunkId).catch(function(error) {
        debugLog('Chunk loading failed, retrying:', chunkId, error.message);
        
        // Try loading with corrected path
        return new Promise(function(resolve, reject) {
          setTimeout(function() {
            originalEnsure.call(this, chunkId)
              .then(resolve)
              .catch(function(retryError) {
                debugLog('Chunk retry failed:', chunkId, retryError.message);
                reject(retryError);
              });
          }.bind(this), 100);
        }.bind(this));
      }.bind(this));
    };
  }
  
  // 4. Fix RSC payload loading
  if (config.enableRSCFix && typeof window.fetch === 'function') {
    const originalFetch = window.fetch;
    window.fetch = function(input, init) {
      let url = typeof input === 'string' ? input : input.url;
      const originalUrl = url;
      
      // Fix RSC payload paths
      if (url.startsWith('/_next/') && !url.startsWith('./')) {
        url = '.' + url;
        
        if (typeof input === 'string') {
          input = url;
        } else {
          input = new Request(url, input);
        }
        
        debugLog('Corrected RSC URL:', originalUrl, '->', url);
      }
      
      return originalFetch.call(this, input, init);
    };
  }
  
  // 5. Fix chunk script filename generation
  if (typeof __webpack_require__.u === 'function') {
    const originalGetChunkScriptFilename = __webpack_require__.u;
    __webpack_require__.u = function(chunkId) {
      const filename = originalGetChunkScriptFilename.call(this, chunkId);
      
      // Ensure chunk filenames are relative for static exports
      if (filename.startsWith('/') && !filename.startsWith('./')) {
        const correctedFilename = '.' + filename;
        debugLog('Corrected chunk filename:', filename, '->', correctedFilename);
        return correctedFilename;
      }
      
      return filename;
    };
  }
  
  // 6. Fix CSS chunk loading
  if (typeof __webpack_require__.miniCssF === 'function') {
    const originalMiniCssF = __webpack_require__.miniCssF;
    __webpack_require__.miniCssF = function(chunkId) {
      const filename = originalMiniCssF.call(this, chunkId);
      
      // Ensure CSS filenames are relative for static exports
      if (filename.startsWith('/') && !filename.startsWith('./')) {
        const correctedFilename = '.' + filename;
        debugLog('Corrected CSS filename:', filename, '->', correctedFilename);
        return correctedFilename;
      }
      
      return filename;
    };
  }
  
  // 7. Handle Next.js specific loading issues
  if (typeof window.__NEXT_DATA__ !== 'undefined') {
    debugLog('Next.js data detected, applying Next.js specific fixes');
    
    // Fix buildId references
    if (window.__NEXT_DATA__.buildId) {
      debugLog('Build ID:', window.__NEXT_DATA__.buildId);
    }
    
    // Ensure router works with static exports
    if (typeof window.next !== 'undefined' && window.next.router) {
      debugLog('Next.js router detected');
    }
  }
  
  // 8. Monitor for loading errors and provide helpful debugging
  window.addEventListener('error', function(event) {
    if (event.filename && (event.filename.includes('_next') || event.filename.includes('chunks'))) {
      debugLog('Asset loading error detected:', {
        filename: event.filename,
        message: event.message,
        lineno: event.lineno,
        colno: event.colno
      });
      
      // Provide helpful error information
      console.error('[Static Export] Asset loading failed:', event.filename);
      console.error('This might be due to incorrect asset paths in static export.');
      console.error('Check that all assets use relative paths starting with "./"');
    }
  });
  
  debugLog('Runtime chunk manager initialization complete');
  
})();
`;
  }

  /**
   * Generate webpack runtime module class
   */
  generateWebpackRuntimeModule(): any {
    const webpack = require('webpack');
    const self = this;
    
    class EnhancedStaticExportRuntimeModule extends webpack.RuntimeModule {
      constructor() {
        super('enhanced-static-export-runtime', 5); // High priority
      }

      generate() {
        return self.generateRuntimeCode();
      }
    }

    return EnhancedStaticExportRuntimeModule;
  }
}

// Default instance for static exports
export const runtimeChunkManager = new RuntimeChunkManager({
  publicPath: './_next/',
  enablePathCorrection: true,
  enableRSCFix: true,
  enableChunkValidation: true,
  debugMode: process.env.NODE_ENV === 'development'
});

// Helper to inject runtime code into HTML
export function injectRuntimeCode(htmlContent: string): string {
  const runtimeCode = runtimeChunkManager.generateRuntimeCode();
  const scriptTag = `<script>${runtimeCode}</script>`;
  
  // Inject before the first script tag or at the end of head
  if (htmlContent.includes('<script')) {
    return htmlContent.replace('<script', scriptTag + '\n<script');
  } else if (htmlContent.includes('</head>')) {
    return htmlContent.replace('</head>', scriptTag + '\n</head>');
  } else {
    return htmlContent + scriptTag;
  }
}