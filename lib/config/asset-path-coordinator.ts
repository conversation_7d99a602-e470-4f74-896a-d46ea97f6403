/**
 * Asset Path Coordinator
 * 
 * Provides unified asset path configuration and resolution for all build targets.
 * Ensures consistent relative paths for static exports while maintaining compatibility
 * with web and server builds.
 */

export enum BuildTarget {
  WEB = 'web',
  ELECTRON = 'electron',
  MOBILE = 'mobile',
  SERVER = 'server'
}

export enum AssetType {
  STATIC = 'static',
  FONT = 'font',
  CHUNK = 'chunk',
  CSS = 'css',
  IMAGE = 'image'
}

export interface AssetPathConfig {
  buildTarget: BuildTarget;
  staticExport: boolean;
  assetPrefix: string;
  publicPath: string;
  fontOptimization: boolean;
  pathValidation: boolean;
}

export interface AssetReference {
  type: AssetType;
  originalPath: string;
  resolvedPath: string;
  isRelative: boolean;
  isValid: boolean;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  type: AssetPathError;
  file: string;
  line?: number;
  originalPath: string;
  expectedPath: string;
  suggestion: string;
}

export interface ValidationWarning {
  type: string;
  message: string;
  file: string;
  path: string;
}

export enum AssetPathError {
  INCONSISTENT_PATHS = 'inconsistent_paths',
  MISSING_FONT_ASSETS = 'missing_font_assets',
  BROKEN_CHUNK_REFERENCES = 'broken_chunk_references',
  INVALID_PUBLIC_PATH = 'invalid_public_path'
}

/**
 * AssetPathCoordinator - Central coordinator for all asset path resolution
 */
export class AssetPathCoordinator {
  private config: AssetPathConfig;

  constructor(buildTarget?: BuildTarget) {
    this.config = this.createConfig(buildTarget);
  }

  /**
   * Create configuration based on build target
   */
  private createConfig(buildTarget?: BuildTarget): AssetPathConfig {
    const target = buildTarget || this.detectBuildTarget();
    const isStaticExport = target === BuildTarget.ELECTRON || target === BuildTarget.MOBILE;

    return {
      buildTarget: target,
      staticExport: isStaticExport,
      assetPrefix: isStaticExport ? './' : '',
      publicPath: isStaticExport ? './_next/' : '/_next/',
      fontOptimization: isStaticExport,
      pathValidation: true
    };
  }

  /**
   * Detect build target from environment
   */
  private detectBuildTarget(): BuildTarget {
    const buildTarget = process.env.BUILD_TARGET;
    
    switch (buildTarget) {
      case 'web':
        return BuildTarget.WEB;
      case 'electron':
        return BuildTarget.ELECTRON;
      case 'mobile':
        return BuildTarget.MOBILE;
      case 'server':
        return BuildTarget.SERVER;
      default:
        // Default to web for unknown targets
        return BuildTarget.WEB;
    }
  }

  /**
   * Get asset prefix for the current build target
   */
  getAssetPrefix(buildTarget?: BuildTarget): string {
    const target = buildTarget || this.config.buildTarget;
    
    switch (target) {
      case BuildTarget.ELECTRON:
      case BuildTarget.MOBILE:
        return './';
      case BuildTarget.WEB:
      case BuildTarget.SERVER:
      default:
        return '';
    }
  }

  /**
   * Get public path for webpack configuration
   */
  getPublicPath(buildTarget?: BuildTarget): string {
    const target = buildTarget || this.config.buildTarget;
    
    switch (target) {
      case BuildTarget.ELECTRON:
      case BuildTarget.MOBILE:
        return './_next/';
      case BuildTarget.WEB:
      case BuildTarget.SERVER:
      default:
        return '/_next/';
    }
  }

  /**
   * Resolve asset path based on type and build target
   */
  resolveAssetPath(assetType: AssetType, path: string, buildTarget?: BuildTarget): string {
    const target = buildTarget || this.config.buildTarget;
    const isStaticExport = target === BuildTarget.ELECTRON || target === BuildTarget.MOBILE;

    // For static exports, ensure all paths are relative
    if (isStaticExport) {
      return this.makePathRelative(path, assetType);
    }

    // For web/server builds, use standard paths
    return path;
  }

  /**
   * Make a path relative for static exports
   */
  private makePathRelative(path: string, assetType: AssetType): string {
    // Remove leading slash if present
    let relativePath = path.startsWith('/') ? path.substring(1) : path;
    
    // Ensure _next paths start with ./
    if (relativePath.startsWith('_next/')) {
      relativePath = './' + relativePath;
    }
    
    // Handle different asset types
    switch (assetType) {
      case AssetType.FONT:
        // Font assets should use relative paths
        if (!relativePath.startsWith('./')) {
          relativePath = './' + relativePath;
        }
        break;
      
      case AssetType.CHUNK:
        // JavaScript chunks should use ./_next/ prefix
        if (!relativePath.startsWith('./_next/')) {
          relativePath = './_next/' + relativePath.replace(/^_next\//, '');
        }
        break;
      
      case AssetType.CSS:
        // CSS files should use ./_next/ prefix
        if (!relativePath.startsWith('./_next/')) {
          relativePath = './_next/' + relativePath.replace(/^_next\//, '');
        }
        break;
      
      case AssetType.STATIC:
      case AssetType.IMAGE:
        // Static assets should use relative paths
        if (!relativePath.startsWith('./')) {
          relativePath = './' + relativePath;
        }
        break;
    }

    return relativePath;
  }

  /**
   * Validate path consistency across multiple paths
   */
  validatePathConsistency(paths: string[]): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Check for mixed absolute/relative paths
    const absolutePaths = paths.filter(p => p.startsWith('/'));
    const relativePaths = paths.filter(p => p.startsWith('./'));
    
    if (this.config.staticExport && absolutePaths.length > 0) {
      absolutePaths.forEach(path => {
        errors.push({
          type: AssetPathError.INCONSISTENT_PATHS,
          file: 'unknown',
          originalPath: path,
          expectedPath: this.makePathRelative(path, AssetType.STATIC),
          suggestion: 'Convert absolute path to relative for static export'
        });
      });
    }

    // Check for missing _next prefix in static exports
    if (this.config.staticExport) {
      const missingPrefixPaths = paths.filter(p => 
        p.includes('static/') && !p.startsWith('./_next/')
      );
      
      missingPrefixPaths.forEach(path => {
        errors.push({
          type: AssetPathError.BROKEN_CHUNK_REFERENCES,
          file: 'unknown',
          originalPath: path,
          expectedPath: './_next/' + path,
          suggestion: 'Add ./_next/ prefix for static export compatibility'
        });
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Get configuration for the current build target
   */
  getConfig(): AssetPathConfig {
    return { ...this.config };
  }

  /**
   * Check if current build is a static export
   */
  isStaticExport(): boolean {
    return this.config.staticExport;
  }

  /**
   * Get font configuration for the current build target
   */
  getFontConfig() {
    return {
      staticExport: this.config.staticExport,
      assetPrefix: this.config.assetPrefix,
      cssVariables: true,
      optimization: this.config.fontOptimization
    };
  }

  /**
   * Get webpack configuration for the current build target
   */
  getWebpackConfig() {
    return {
      output: {
        publicPath: this.config.publicPath,
        assetModuleFilename: this.config.staticExport 
          ? 'static/media/[name].[hash][ext]'
          : 'static/media/[name].[hash][ext]'
      },
      resolve: {
        alias: {}
      }
    };
  }
}

// Singleton instance for global use
export const assetPathCoordinator = new AssetPathCoordinator();

// Helper functions for easy access
export const getAssetPrefix = (buildTarget?: BuildTarget) => 
  assetPathCoordinator.getAssetPrefix(buildTarget);

export const getPublicPath = (buildTarget?: BuildTarget) => 
  assetPathCoordinator.getPublicPath(buildTarget);

export const resolveAssetPath = (assetType: AssetType, path: string, buildTarget?: BuildTarget) => 
  assetPathCoordinator.resolveAssetPath(assetType, path, buildTarget);

export const validateAssetPaths = (paths: string[]) => 
  assetPathCoordinator.validatePathConsistency(paths);

export const isStaticExport = () => 
  assetPathCoordinator.isStaticExport();