/**
 * RSC Payload Fixer
 * 
 * Fixes React Server Components payload loading issues in static exports.
 * This is critical for preventing blank screens in Electron and mobile builds.
 */

export interface RSCFixerConfig {
  enablePathCorrection: boolean;
  enablePayloadValidation: boolean;
  enableFallbackHandling: boolean;
  debugMode: boolean;
}

export class RSCPayloadFixer {
  private config: RSCFixerConfig;

  constructor(config: Partial<RSCFixerConfig> = {}) {
    this.config = {
      enablePathCorrection: true,
      enablePayloadValidation: true,
      enableFallbackHandling: true,
      debugMode: false,
      ...config
    };
  }

  /**
   * Generate runtime code to fix RSC payload loading
   */
  generateRuntimeCode(): string {
    return `
// RSC Payload Fixer - Critical for preventing blank screens
(function() {
  'use strict';
  
  const config = ${JSON.stringify(this.config)};
  const isStaticExport = typeof window !== 'undefined';
  
  if (!isStaticExport) return;
  
  function debugLog(message, ...args) {
    if (config.debugMode) {
      console.log('[RSCPayloadFixer]', message, ...args);
    }
  }
  
  debugLog('Initializing RSC payload fixer');
  
  // 1. Fix fetch requests for RSC payloads
  if (config.enablePathCorrection && typeof window.fetch === 'function') {
    const originalFetch = window.fetch;
    window.fetch = function(input, init) {
      let url = typeof input === 'string' ? input : input.url;
      const originalUrl = url;
      
      // Fix RSC payload paths
      if (url.includes('/_next/static/chunks/') && !url.startsWith('./')) {
        url = url.replace(/^\\//, './');
        
        if (typeof input === 'string') {
          input = url;
        } else {
          input = new Request(url, input);
        }
        
        debugLog('Corrected RSC payload URL:', originalUrl, '->', url);
      }
      
      // Fix app-pages-browser paths
      if (url.includes('app-pages-browser') && !url.startsWith('./')) {
        url = url.replace(/^\\//, './');
        
        if (typeof input === 'string') {
          input = url;
        } else {
          input = new Request(url, input);
        }
        
        debugLog('Corrected app-pages-browser URL:', originalUrl, '->', url);
      }
      
      return originalFetch.call(this, input, init).catch(function(error) {
        if (config.enableFallbackHandling) {
          debugLog('Fetch failed, attempting fallback:', originalUrl, error.message);
          
          // Try alternative path resolution
          let fallbackUrl = originalUrl;
          if (fallbackUrl.startsWith('/') && !fallbackUrl.startsWith('./')) {
            fallbackUrl = '.' + fallbackUrl;
            
            const fallbackInput = typeof input === 'string' ? fallbackUrl : new Request(fallbackUrl, input);
            return originalFetch.call(this, fallbackInput, init);
          }
        }
        
        throw error;
      });
    };
  }
  
  // 2. Fix Next.js router for static exports
  if (typeof window.__NEXT_DATA__ !== 'undefined') {
    debugLog('Fixing Next.js data for static export');
    
    // Ensure buildManifest paths are relative
    if (window.__NEXT_DATA__.buildManifest) {
      const manifest = window.__NEXT_DATA__.buildManifest;
      
      // Fix pages paths
      if (manifest.pages) {
        Object.keys(manifest.pages).forEach(function(page) {
          if (Array.isArray(manifest.pages[page])) {
            manifest.pages[page] = manifest.pages[page].map(function(path) {
              if (path.startsWith('/') && !path.startsWith('./')) {
                return '.' + path;
              }
              return path;
            });
          }
        });
      }
      
      // Fix low priority files
      if (manifest.lowPriorityFiles) {
        manifest.lowPriorityFiles = manifest.lowPriorityFiles.map(function(path) {
          if (path.startsWith('/') && !path.startsWith('./')) {
            return '.' + path;
          }
          return path;
        });
      }
      
      debugLog('Fixed buildManifest paths');
    }
  }
  
  // 3. Fix webpack chunk loading for RSC
  if (typeof __webpack_require__ !== 'undefined') {
    // Override chunk loading to handle RSC payloads
    if (typeof __webpack_require__.f !== 'undefined' && __webpack_require__.f.j) {
      const originalJsonpChunkLoading = __webpack_require__.f.j;
      __webpack_require__.f.j = function(chunkId, promises) {
        debugLog('Loading RSC chunk:', chunkId);
        
        // Apply the original loading with error handling
        try {
          return originalJsonpChunkLoading.call(this, chunkId, promises);
        } catch (error) {
          debugLog('RSC chunk loading failed:', chunkId, error.message);
          throw error;
        }
      };
    }
  }
  
  // 4. Handle Next.js App Router specific issues
  if (typeof window.next !== 'undefined') {
    debugLog('Applying Next.js App Router fixes');
    
    // Monitor for navigation errors
    if (window.next.router) {
      const originalPush = window.next.router.push;
      if (originalPush) {
        window.next.router.push = function(url, as, options) {
          debugLog('Router push:', url, as);
          return originalPush.call(this, url, as, options);
        };
      }
    }
  }
  
  // 5. Fix React hydration issues
  if (typeof window.React !== 'undefined' || typeof window.__REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined') {
    debugLog('Applying React hydration fixes');
    
    // Monitor for hydration errors
    window.addEventListener('error', function(event) {
      if (event.message && event.message.includes('Hydration')) {
        debugLog('Hydration error detected:', event.message);
        console.warn('[Static Export] Hydration error - this may cause blank screens');
      }
    });
  }
  
  // 6. Validate RSC payload structure
  if (config.enablePayloadValidation) {
    // Monitor XHR/fetch responses for RSC payloads
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
      const originalUrl = url;
      
      if (typeof url === 'string' && url.includes('/_next/')) {
        debugLog('XHR request to Next.js asset:', url);
      }
      
      return originalXHROpen.call(this, method, url, async, user, password);
    };
  }
  
  debugLog('RSC payload fixer initialization complete');
  
})();
`;
  }

  /**
   * Process HTML content to inject RSC fixes
   */
  processHTML(htmlContent: string): string {
    const runtimeCode = this.generateRuntimeCode();
    const scriptTag = `<script>${runtimeCode}</script>`;
    
    // Inject before Next.js scripts
    if (htmlContent.includes('/_next/static/chunks/')) {
      return htmlContent.replace(
        /<script[^>]+src=["'][^"']*\/_next\/static\/chunks\/[^"']*["'][^>]*><\/script>/,
        scriptTag + '\n$&'
      );
    }
    
    // Fallback: inject at end of head
    if (htmlContent.includes('</head>')) {
      return htmlContent.replace('</head>', scriptTag + '\n</head>');
    }
    
    return htmlContent + scriptTag;
  }
}

// Default instance for static exports
export const rscPayloadFixer = new RSCPayloadFixer({
  enablePathCorrection: true,
  enablePayloadValidation: true,
  enableFallbackHandling: true,
  debugMode: process.env.NODE_ENV === 'development'
});

// Helper function to apply RSC fixes to HTML
export function applyRSCFixes(htmlContent: string): string {
  return rscPayloadFixer.processHTML(htmlContent);
}