/**
 * Tests for Asset Path Coordinator
 */

import { AssetPathCoordinator, BuildTarget, AssetType } from '../asset-path-coordinator';

describe('AssetPathCoordinator', () => {
  describe('Build Target Detection', () => {
    it('should detect electron build target', () => {
      process.env.BUILD_TARGET = 'electron';
      const coordinator = new AssetPathCoordinator();
      expect(coordinator.getConfig().buildTarget).toBe(BuildTarget.ELECTRON);
      expect(coordinator.isStaticExport()).toBe(true);
    });

    it('should detect mobile build target', () => {
      process.env.BUILD_TARGET = 'mobile';
      const coordinator = new AssetPathCoordinator();
      expect(coordinator.getConfig().buildTarget).toBe(BuildTarget.MOBILE);
      expect(coordinator.isStaticExport()).toBe(true);
    });

    it('should detect web build target', () => {
      process.env.BUILD_TARGET = 'web';
      const coordinator = new AssetPathCoordinator();
      expect(coordinator.getConfig().buildTarget).toBe(BuildTarget.WEB);
      expect(coordinator.isStaticExport()).toBe(false);
    });

    it('should default to web for unknown targets', () => {
      process.env.BUILD_TARGET = 'unknown';
      const coordinator = new AssetPathCoordinator();
      expect(coordinator.getConfig().buildTarget).toBe(BuildTarget.WEB);
      expect(coordinator.isStaticExport()).toBe(false);
    });
  });

  describe('Asset Prefix Generation', () => {
    it('should return relative prefix for static exports', () => {
      const coordinator = new AssetPathCoordinator(BuildTarget.ELECTRON);
      expect(coordinator.getAssetPrefix()).toBe('./');
    });

    it('should return empty prefix for web builds', () => {
      const coordinator = new AssetPathCoordinator(BuildTarget.WEB);
      expect(coordinator.getAssetPrefix()).toBe('');
    });
  });

  describe('Public Path Generation', () => {
    it('should return relative public path for static exports', () => {
      const coordinator = new AssetPathCoordinator(BuildTarget.ELECTRON);
      expect(coordinator.getPublicPath()).toBe('./_next/');
    });

    it('should return absolute public path for web builds', () => {
      const coordinator = new AssetPathCoordinator(BuildTarget.WEB);
      expect(coordinator.getPublicPath()).toBe('/_next/');
    });
  });

  describe('Asset Path Resolution', () => {
    it('should make paths relative for static exports', () => {
      const coordinator = new AssetPathCoordinator(BuildTarget.ELECTRON);
      
      expect(coordinator.resolveAssetPath(AssetType.CHUNK, '/static/chunks/main.js'))
        .toBe('./_next/static/chunks/main.js');
      
      expect(coordinator.resolveAssetPath(AssetType.CSS, '/static/css/main.css'))
        .toBe('./_next/static/css/main.css');
      
      expect(coordinator.resolveAssetPath(AssetType.FONT, '/fonts/inter.woff2'))
        .toBe('./fonts/inter.woff2');
    });

    it('should preserve paths for web builds', () => {
      const coordinator = new AssetPathCoordinator(BuildTarget.WEB);
      
      expect(coordinator.resolveAssetPath(AssetType.CHUNK, '/static/chunks/main.js'))
        .toBe('/static/chunks/main.js');
      
      expect(coordinator.resolveAssetPath(AssetType.CSS, '/static/css/main.css'))
        .toBe('/static/css/main.css');
    });
  });

  describe('Path Validation', () => {
    it('should detect inconsistent paths in static exports', () => {
      const coordinator = new AssetPathCoordinator(BuildTarget.ELECTRON);
      const paths = [
        './_next/static/chunks/main.js',
        '/static/chunks/other.js', // This should be flagged
        './static/css/main.css'
      ];
      
      const result = coordinator.validatePathConsistency(paths);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('inconsistent_paths');
    });

    it('should pass validation for consistent relative paths', () => {
      const coordinator = new AssetPathCoordinator(BuildTarget.ELECTRON);
      const paths = [
        './_next/static/chunks/main.js',
        './_next/static/css/main.css',
        './static/images/logo.png'
      ];
      
      const result = coordinator.validatePathConsistency(paths);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  afterEach(() => {
    delete process.env.BUILD_TARGET;
  });
});