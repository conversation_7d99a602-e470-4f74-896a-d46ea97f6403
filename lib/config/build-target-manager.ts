/**
 * Build Target Configuration Manager
 * 
 * Manages automatic configuration switching based on BUILD_TARGET environment variable.
 * Ensures backward compatibility with existing build scripts while providing
 * enhanced asset path handling.
 */

import { BuildTarget } from './asset-path-coordinator';

export interface BuildTargetConfig {
  target: BuildTarget;
  isStaticExport: boolean;
  requiresRelativePaths: boolean;
  supportsDynamicImports: boolean;
  supportsServerFeatures: boolean;
  optimizeFonts: boolean;
  optimizeImages: boolean;
}

export class BuildTargetManager {
  private currentTarget: BuildTarget;
  private config: BuildTargetConfig;

  constructor() {
    this.currentTarget = this.detectBuildTarget();
    this.config = this.createTargetConfig(this.currentTarget);
  }

  /**
   * Detect build target from environment
   */
  private detectBuildTarget(): BuildTarget {
    const buildTarget = process.env.BUILD_TARGET?.toLowerCase();
    
    switch (buildTarget) {
      case 'web':
        return BuildTarget.WEB;
      case 'electron':
        return BuildTarget.ELECTRON;
      case 'mobile':
        return BuildTarget.MOBILE;
      case 'server':
        return BuildTarget.SERVER;
      default:
        // Default to web for unknown or missing targets
        console.warn(`Unknown BUILD_TARGET: ${buildTarget}, defaulting to 'web'`);
        return BuildTarget.WEB;
    }
  }

  /**
   * Create configuration for the detected build target
   */
  private createTargetConfig(target: BuildTarget): BuildTargetConfig {
    const baseConfig = {
      target,
      isStaticExport: false,
      requiresRelativePaths: false,
      supportsDynamicImports: true,
      supportsServerFeatures: true,
      optimizeFonts: true,
      optimizeImages: true
    };

    switch (target) {
      case BuildTarget.WEB:
        return {
          ...baseConfig,
          // Web builds use standard Next.js configuration
          supportsServerFeatures: true,
          optimizeImages: true
        };

      case BuildTarget.ELECTRON:
        return {
          ...baseConfig,
          isStaticExport: true,
          requiresRelativePaths: true,
          supportsServerFeatures: false,
          optimizeImages: false, // Unoptimized for static export
          optimizeFonts: true
        };

      case BuildTarget.MOBILE:
        return {
          ...baseConfig,
          isStaticExport: true,
          requiresRelativePaths: true,
          supportsServerFeatures: false,
          optimizeImages: false, // Unoptimized for static export
          optimizeFonts: true
        };

      case BuildTarget.SERVER:
        return {
          ...baseConfig,
          supportsServerFeatures: true,
          supportsDynamicImports: true,
          optimizeImages: true
        };

      default:
        return baseConfig;
    }
  }

  /**
   * Get current build target
   */
  getBuildTarget(): BuildTarget {
    return this.currentTarget;
  }

  /**
   * Get current build configuration
   */
  getConfig(): BuildTargetConfig {
    return { ...this.config };
  }

  /**
   * Check if current build is a static export
   */
  isStaticExport(): boolean {
    return this.config.isStaticExport;
  }

  /**
   * Check if current build requires relative paths
   */
  requiresRelativePaths(): boolean {
    return this.config.requiresRelativePaths;
  }

  /**
   * Check if current build supports server features
   */
  supportsServerFeatures(): boolean {
    return this.config.supportsServerFeatures;
  }

  /**
   * Check if current build supports dynamic imports
   */
  supportsDynamicImports(): boolean {
    return this.config.supportsDynamicImports;
  }

  /**
   * Get Next.js configuration overrides for current target
   */
  getNextConfigOverrides() {
    const config = this.config;
    
    if (config.isStaticExport) {
      return {
        output: 'export' as const,
        trailingSlash: true,
        images: { unoptimized: !config.optimizeImages },
        experimental: {
          optimizeCss: false,
          esmExternals: true
        }
      };
    }

    return {
      images: {
        domains: ['res.cloudinary.com'],
        unoptimized: !config.optimizeImages
      }
    };
  }

  /**
   * Get webpack configuration overrides for current target
   */
  getWebpackConfigOverrides() {
    const config = this.config;
    
    return {
      publicPath: config.requiresRelativePaths ? './_next/' : '/_next/',
      assetModuleFilename: 'static/media/[name].[hash][ext]',
      chunkFilename: config.requiresRelativePaths 
        ? 'static/chunks/[name].[contenthash].js'
        : 'static/chunks/[name].[contenthash].js',
      fallbacks: config.supportsServerFeatures ? {} : {
        fs: false,
        path: false,
        crypto: false,
        stream: false,
        net: false,
        tls: false,
        child_process: false,
      }
    };
  }

  /**
   * Validate current build target configuration
   */
  validateConfiguration(): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check environment consistency
    if (this.config.isStaticExport && process.env.NODE_ENV !== 'production') {
      warnings.push('Static exports should typically be built in production mode');
    }

    // Check for conflicting configurations
    if (this.config.isStaticExport && this.config.supportsServerFeatures) {
      errors.push('Static exports cannot support server features');
    }

    // Check for missing dependencies
    if (this.config.requiresRelativePaths && !this.config.isStaticExport) {
      warnings.push('Relative paths are typically only needed for static exports');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Get build script compatibility information
   */
  getBuildScriptCompatibility() {
    return {
      target: this.currentTarget,
      requiresPostBuildScripts: false, // We handle everything natively now
      compatibleScripts: this.getCompatibleScripts(),
      deprecatedScripts: this.getDeprecatedScripts()
    };
  }

  private getCompatibleScripts(): string[] {
    switch (this.currentTarget) {
      case BuildTarget.WEB:
        return ['build:web', 'build:static'];
      case BuildTarget.ELECTRON:
        return ['build:electron', 'electron:build'];
      case BuildTarget.MOBILE:
        return ['build:mobile', 'cap:build:android', 'cap:build:ios'];
      case BuildTarget.SERVER:
        return ['build', 'start'];
      default:
        return [];
    }
  }

  private getDeprecatedScripts(): string[] {
    // Scripts that are no longer needed due to native handling
    return [
      'fix-rsc-chunk-paths.js',
      'fix-electron-paths.js',
      'electron-runtime-fix.js'
    ];
  }
}

// Singleton instance
export const buildTargetManager = new BuildTargetManager();

// Helper functions
export const getCurrentBuildTarget = () => buildTargetManager.getBuildTarget();
export const isStaticExportBuild = () => buildTargetManager.isStaticExport();
export const requiresRelativePathsBuild = () => buildTargetManager.requiresRelativePaths();
export const getNextConfigOverrides = () => buildTargetManager.getNextConfigOverrides();
export const getWebpackConfigOverrides = () => buildTargetManager.getWebpackConfigOverrides();
export const validateBuildConfiguration = () => buildTargetManager.validateConfiguration();